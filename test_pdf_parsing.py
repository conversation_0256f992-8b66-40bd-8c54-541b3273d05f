#!/usr/bin/env python3
"""
Test script to verify improved PDF parsing and email extraction.
"""
from src.pdf_reader import PDFReader
from src.comprehensive_analyzer import <PERSON><PERSON>nal<PERSON><PERSON>

def test_text_cleaning():
    """Test the text cleaning functionality."""
    print("🧪 Testing Text Cleaning Functionality")
    print("=" * 50)
    
    pdf_reader = PDFReader()
    
    # Test cases with common PDF extraction issues
    test_cases = [
        {
            'name': 'Broken Email',
            'input': 'Contact me at john . doe @ company . com for more info',
            'expected_pattern': r'john\.doe@company\.com'
        },
        {
            'name': 'Broken Phone',
            'input': 'Phone: 555 - 123 - 4567 or call me',
            'expected_pattern': r'************'
        },
        {
            'name': 'Broken LinkedIn',
            'input': 'Find me on linkedin . com / in / johndoe',
            'expected_pattern': r'linkedin\.com/in/johndoe'
        },
        {
            'name': 'Broken GitHub',
            'input': 'My code is at github . com / johndoe',
            'expected_pattern': r'github\.com/johndoe'
        },
        {
            'name': 'Excessive Whitespace',
            'input': 'Python    JavaScript     React    with   lots   of   spaces',
            'expected_pattern': r'Python JavaScript React with lots of spaces'
        }
    ]
    
    for test_case in test_cases:
        print(f"\n{test_case['name']}:")
        print(f"  Input:  '{test_case['input']}'")
        
        cleaned = pdf_reader._clean_extracted_text(test_case['input'])
        print(f"  Output: '{cleaned}'")
        
        import re
        if re.search(test_case['expected_pattern'], cleaned, re.IGNORECASE):
            print("  ✅ PASS")
        else:
            print("  ❌ FAIL")

def test_email_extraction():
    """Test enhanced email extraction."""
    print("\n\n📧 Testing Enhanced Email Extraction")
    print("=" * 50)
    
    analyzer = ComprehensiveAnalyzer()
    
    test_texts = [
        "Contact John <NAME_EMAIL> for more information",
        "Email: <EMAIL>",
        "Reach me at: <EMAIL>",
        "john . doe @ company . com",  # Broken email from PDF
        "My <NAME_EMAIL>",
        """
        John Smith
        Senior Developer
        Email: <EMAIL>
        Phone: (*************
        """,
        "Contact info: <EMAIL> | Phone: 555-1234",
        "<EMAIL>"
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\nTest {i}:")
        print(f"  Text: {text.strip()}")
        
        personal_info = analyzer.extract_personal_information(text)
        email = personal_info.get('email')
        
        if email:
            print(f"  ✅ Found email: {email}")
        else:
            print(f"  ❌ No email found")

def test_phone_extraction():
    """Test enhanced phone extraction."""
    print("\n\n📞 Testing Enhanced Phone Extraction")
    print("=" * 50)
    
    analyzer = ComprehensiveAnalyzer()
    
    test_texts = [
        "Phone: (*************",
        "Call me at ************",
        "Mobile: +1-************",
        "Tel: ************",
        "************",  # Spaces
        "5551234567",    # No separators
        "+1 (*************",
        "Phone: +44 20 7946 0958",  # International
        "Contact: 555 - 123 - 4567",  # Broken from PDF
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\nTest {i}:")
        print(f"  Text: {text}")
        
        personal_info = analyzer.extract_personal_information(text)
        phone = personal_info.get('phone')
        
        if phone:
            print(f"  ✅ Found phone: {phone}")
        else:
            print(f"  ❌ No phone found")

def test_skill_variations():
    """Test skill extraction with variations."""
    print("\n\n🛠️ Testing Skill Extraction Variations")
    print("=" * 50)
    
    from src.ai_analyzer import AIAnalyzer
    
    # Mock skills data for testing
    test_skills = [
        {'skill': 'JavaScript', 'confidence': 0.9, 'source': 'test'},
        {'skill': 'PostgreSQL', 'confidence': 0.9, 'source': 'test'},
        {'skill': 'Kubernetes', 'confidence': 0.9, 'source': 'test'},
        {'skill': 'MongoDB', 'confidence': 0.9, 'source': 'test'},
    ]
    
    test_text = """
    I have 5 years of experience with JavaScript (JS) and 3 years with TypeScript.
    Database experience includes PostgreSQL (4 years) and MongoDB (2 years).
    DevOps skills: Docker (3 years), Kubernetes/K8s (2 years).
    Frontend: React (4 years), Vue.js (1 year).
    """
    
    analyzer = AIAnalyzer()
    experience_data = analyzer.extract_experience_years(test_text, test_skills)
    
    print("Experience extraction results:")
    for skill, data in experience_data.items():
        years = data.get('years')
        if years:
            print(f"  ✅ {skill}: {years} years")
        else:
            print(f"  ❌ {skill}: No experience found")

def create_sample_resume_text():
    """Create a sample resume text for comprehensive testing."""
    return """
    JOHN DOE
    Senior Software Engineer
    
    Email: <EMAIL>
    Phone: (*************
    Location: San Francisco, CA
    LinkedIn: linkedin.com/in/johndoe
    GitHub: github.com/johndoe
    
    PROFESSIONAL SUMMARY
    Experienced software engineer with 8+ years in full-stack development.
    
    TECHNICAL SKILLS
    • Programming Languages: Python (6 years), JavaScript (5 years), TypeScript (3 years)
    • Frontend: React (4 years), Vue.js (2 years), HTML/CSS (6 years)
    • Backend: Django (5 years), Flask (3 years), Node.js (3 years)
    • Databases: PostgreSQL (5 years), MongoDB (3 years), Redis (2 years)
    • Cloud: AWS (4 years), Docker (3 years), Kubernetes (2 years)
    • Tools: Git, Jenkins, JIRA
    
    EXPERIENCE
    Senior Software Engineer | TechCorp | 2020-Present
    • Led development using React and Python
    • Implemented microservices with Docker and Kubernetes
    
    Software Engineer | StartupXYZ | 2018-2020
    • Built web applications with Django and PostgreSQL
    • Worked with JavaScript and React for 2 years
    """

def test_comprehensive_parsing():
    """Test comprehensive parsing with sample resume."""
    print("\n\n🔍 Testing Comprehensive Resume Parsing")
    print("=" * 50)
    
    sample_text = create_sample_resume_text()
    
    # Test personal info extraction
    analyzer = ComprehensiveAnalyzer()
    personal_info = analyzer.extract_personal_information(sample_text)
    
    print("Personal Information Extraction:")
    for key, value in personal_info.items():
        if value:
            print(f"  ✅ {key}: {value}")
        else:
            print(f"  ❌ {key}: Not found")
    
    # Test skill extraction
    from src.ai_analyzer import AIAnalyzer
    ai_analyzer = AIAnalyzer()
    
    # Get entities (this would normally use the AI model)
    entities = []  # Mock empty for this test
    
    # Extract skills
    skills = ai_analyzer.extract_skills_from_entities(entities, sample_text)
    skills_with_experience = ai_analyzer.extract_experience_years(sample_text, skills)
    
    print(f"\nSkill Extraction Results:")
    print(f"  Total skills found: {len(skills_with_experience)}")
    
    # Show top 10 skills with experience
    sorted_skills = sorted(
        skills_with_experience.items(),
        key=lambda x: (x[1].get('years', 0) or 0, x[1].get('confidence', 0)),
        reverse=True
    )
    
    print("  Top skills with experience:")
    for skill, data in sorted_skills[:10]:
        years = data.get('years', 0) or 0
        confidence = data.get('confidence', 0)
        print(f"    • {skill}: {years} years (confidence: {confidence:.2f})")

if __name__ == "__main__":
    print("🚀 Enhanced PDF Parsing and Text Extraction Tests")
    print("=" * 60)
    
    # Run all tests
    test_text_cleaning()
    test_email_extraction()
    test_phone_extraction()
    test_skill_variations()
    test_comprehensive_parsing()
    
    print("\n" + "=" * 60)
    print("✅ All tests completed! Check the results above.")
    print("💡 If you see failures, the patterns may need further refinement.")
