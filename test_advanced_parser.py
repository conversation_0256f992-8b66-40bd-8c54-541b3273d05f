#!/usr/bin/env python3
"""
Test script for the advanced PDF parser.
"""
import sys
import os
from src.advanced_pdf_parser import AdvancedPDFParser

def test_advanced_parser(pdf_path: str):
    """Test the advanced PDF parser with a real PDF file."""
    
    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        return
    
    print(f"🔍 Testing Advanced PDF Parser with: {pdf_path}")
    print("=" * 60)
    
    parser = AdvancedPDFParser()
    
    # Extract text using all methods
    result = parser.extract_text_comprehensive(pdf_path)
    
    print(f"\n📊 EXTRACTION RESULTS:")
    print(f"  Success: {result['success']}")
    print(f"  Best Method: {result.get('method_used', 'None')}")
    print(f"  Confidence Score: {result.get('confidence_score', 0):.2f}/100")
    print(f"  Text Length: {len(result.get('text', ''))}")
    
    if result.get('error'):
        print(f"  Error: {result['error']}")
    
    # Show results from all methods
    print(f"\n🔧 ALL EXTRACTION METHODS:")
    all_methods = result.get('all_methods', {})
    for method, method_result in all_methods.items():
        if method_result.get('success'):
            print(f"  ✅ {method}: {method_result.get('length', 0)} chars, score: {method_result.get('score', 0):.2f}")
        else:
            print(f"  ❌ {method}: {method_result.get('error', 'Failed')}")
    
    # Extract structured data if successful
    if result['success']:
        text = result['text']
        structured_data = parser.extract_structured_data(text)
        
        print(f"\n📧 STRUCTURED DATA EXTRACTION:")
        print(f"  Emails: {structured_data.get('emails', [])}")
        print(f"  Phones: {structured_data.get('phones', [])}")
        print(f"  Names: {structured_data.get('names', [])}")
        print(f"  URLs: {structured_data.get('urls', [])}")
        
        # Show first 500 characters of extracted text
        print(f"\n📄 EXTRACTED TEXT PREVIEW:")
        preview = text[:500] + "..." if len(text) > 500 else text
        print(f"  {preview}")
        
        # Test email detection specifically
        print(f"\n🎯 EMAIL DETECTION TEST:")
        import re
        email_patterns = [
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            r'(?:Email|E-mail|Mail):\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
        ]
        
        for i, pattern in enumerate(email_patterns, 1):
            matches = re.findall(pattern, text, re.IGNORECASE)
            print(f"  Pattern {i}: {matches}")
        
        # Save extracted text for manual inspection
        output_file = f"extracted_text_{os.path.basename(pdf_path)}.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"Extraction Method: {result['method_used']}\n")
            f.write(f"Confidence Score: {result['confidence_score']:.2f}\n")
            f.write(f"Text Length: {len(text)}\n")
            f.write("\n" + "="*50 + "\n")
            f.write(text)
        
        print(f"\n💾 Full extracted text saved to: {output_file}")
    
    print("\n" + "="*60)
    return result

def main():
    """Main function to test with command line argument."""
    if len(sys.argv) != 2:
        print("Usage: python test_advanced_parser.py <pdf_file>")
        print("Example: python test_advanced_parser.py 'Hoang Phan CV.pdf'")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    result = test_advanced_parser(pdf_path)
    
    if result['success']:
        print("✅ Advanced PDF parsing test completed successfully!")
        
        # Quick email check
        structured_data = result.get('structured_data')
        if structured_data and structured_data.get('emails'):
            print(f"🎉 Email successfully detected: {structured_data['emails'][0]}")
        else:
            print("⚠️  No email detected - may need manual inspection of extracted text")
    else:
        print("❌ Advanced PDF parsing failed!")
        print("💡 Try installing additional dependencies:")
        print("   pip install pymupdf pytesseract pdf2image pdfminer.six")

if __name__ == "__main__":
    main()
