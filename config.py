"""
Configuration settings for the resume screening application.
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for resume screening application."""

    # AI Model settings - Multiple models for different tasks
    DEFAULT_NER_MODEL = "dbmdz/bert-large-cased-finetuned-conll03-english"
    SKILLS_NER_MODEL = "microsoft/DialoGPT-medium"  # Better for conversational context
    RESUME_SPECIFIC_MODEL = "sentence-transformers/all-MiniLM-L6-v2"  # For semantic similarity

    # Model selection based on task
    SKILLS_MODEL_NAME = os.getenv("SKILLS_MODEL_NAME", DEFAULT_NER_MODEL)
    SEMANTIC_MODEL_NAME = os.getenv("SEMANTIC_MODEL_NAME", RESUME_SPECIFIC_MODEL)
    
    # PDF processing settings
    MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", "10"))
    SUPPORTED_FORMATS = [".pdf"]
    
    # Skills extraction settings
    MIN_CONFIDENCE_SCORE = float(os.getenv("MIN_CONFIDENCE_SCORE", "0.7"))
    
    # Comprehensive software development skills with role mappings
    SOFTWARE_SKILLS = {
        "programming_languages": [
            "python", "java", "javascript", "typescript", "c++", "c#", "go",
            "rust", "php", "ruby", "swift", "kotlin", "scala", "r", "matlab",
            "sql", "html", "css", "dart", "perl", "lua", "haskell", "clojure"
        ],
        "web_frameworks": [
            "react", "angular", "vue", "svelte", "next.js", "nuxt.js", "gatsby",
            "django", "flask", "fastapi", "spring", "spring boot", "express",
            "laravel", "rails", "asp.net", "blazor", "ember.js"
        ],
        "mobile_frameworks": [
            "react native", "flutter", "ionic", "xamarin", "cordova", "phonegap",
            "swift ui", "android sdk", "kotlin multiplatform"
        ],
        "data_science_ml": [
            "tensorflow", "pytorch", "keras", "scikit-learn", "pandas", "numpy",
            "matplotlib", "seaborn", "plotly", "jupyter", "anaconda", "spark",
            "hadoop", "airflow", "mlflow", "kubeflow", "dask", "xgboost"
        ],
        "databases": [
            "mysql", "postgresql", "mongodb", "redis", "elasticsearch",
            "oracle", "sqlite", "cassandra", "dynamodb", "neo4j", "influxdb",
            "snowflake", "bigquery", "redshift", "clickhouse", "couchbase"
        ],
        "cloud_platforms": [
            "aws", "azure", "gcp", "google cloud", "alibaba cloud", "digitalocean",
            "heroku", "vercel", "netlify", "cloudflare"
        ],
        "devops_tools": [
            "docker", "kubernetes", "terraform", "ansible", "jenkins", "gitlab ci",
            "github actions", "circleci", "travis ci", "helm", "istio", "prometheus",
            "grafana", "elk stack", "datadog", "new relic"
        ],
        "development_tools": [
            "git", "svn", "mercurial", "jira", "confluence", "slack", "teams",
            "linux", "unix", "bash", "powershell", "vim", "vscode", "intellij",
            "eclipse", "postman", "swagger", "figma", "sketch"
        ],
        "testing_frameworks": [
            "jest", "mocha", "chai", "cypress", "selenium", "pytest", "unittest",
            "junit", "testng", "cucumber", "jasmine", "karma", "protractor"
        ],
        "big_data": [
            "apache spark", "hadoop", "kafka", "storm", "flink", "hive", "pig",
            "hbase", "zookeeper", "yarn", "hdfs", "parquet", "avro"
        ]
    }

    # Role-specific skill requirements and weights
    ROLE_SKILL_MAPPING = {
        "data_scientist": {
            "required_skills": ["python", "r", "sql", "pandas", "numpy", "scikit-learn"],
            "preferred_skills": ["tensorflow", "pytorch", "jupyter", "matplotlib", "seaborn"],
            "skill_weights": {
                "programming_languages": 0.25,
                "data_science_ml": 0.35,
                "databases": 0.15,
                "cloud_platforms": 0.10,
                "big_data": 0.15
            },
            "min_experience": 2,
            "keywords": ["machine learning", "data analysis", "statistics", "modeling", "algorithms"]
        },
        "data_engineer": {
            "required_skills": ["python", "sql", "spark", "airflow", "kafka"],
            "preferred_skills": ["aws", "docker", "kubernetes", "terraform", "hadoop"],
            "skill_weights": {
                "programming_languages": 0.20,
                "databases": 0.25,
                "big_data": 0.25,
                "cloud_platforms": 0.20,
                "devops_tools": 0.10
            },
            "min_experience": 2,
            "keywords": ["etl", "data pipeline", "data warehouse", "streaming", "batch processing"]
        },
        "full_stack_developer": {
            "required_skills": ["javascript", "html", "css", "react", "node.js"],
            "preferred_skills": ["typescript", "docker", "aws", "mongodb", "postgresql"],
            "skill_weights": {
                "programming_languages": 0.25,
                "web_frameworks": 0.30,
                "databases": 0.15,
                "cloud_platforms": 0.15,
                "development_tools": 0.15
            },
            "min_experience": 1,
            "keywords": ["frontend", "backend", "api", "responsive design", "user interface"]
        },
        "backend_developer": {
            "required_skills": ["java", "python", "sql", "spring", "api"],
            "preferred_skills": ["docker", "kubernetes", "aws", "redis", "postgresql"],
            "skill_weights": {
                "programming_languages": 0.30,
                "web_frameworks": 0.25,
                "databases": 0.20,
                "cloud_platforms": 0.15,
                "devops_tools": 0.10
            },
            "min_experience": 1,
            "keywords": ["api", "microservices", "server", "database", "scalability"]
        },
        "frontend_developer": {
            "required_skills": ["javascript", "html", "css", "react"],
            "preferred_skills": ["typescript", "vue", "angular", "webpack", "sass"],
            "skill_weights": {
                "programming_languages": 0.25,
                "web_frameworks": 0.40,
                "development_tools": 0.20,
                "testing_frameworks": 0.15
            },
            "min_experience": 1,
            "keywords": ["ui", "ux", "responsive", "component", "user experience"]
        },
        "devops_engineer": {
            "required_skills": ["docker", "kubernetes", "aws", "terraform", "jenkins"],
            "preferred_skills": ["ansible", "prometheus", "grafana", "helm", "istio"],
            "skill_weights": {
                "devops_tools": 0.40,
                "cloud_platforms": 0.30,
                "programming_languages": 0.15,
                "databases": 0.15
            },
            "min_experience": 2,
            "keywords": ["ci/cd", "infrastructure", "automation", "monitoring", "deployment"]
        },
        "mobile_developer": {
            "required_skills": ["swift", "kotlin", "react native"],
            "preferred_skills": ["flutter", "ios", "android", "firebase", "xcode"],
            "skill_weights": {
                "programming_languages": 0.30,
                "mobile_frameworks": 0.40,
                "development_tools": 0.20,
                "cloud_platforms": 0.10
            },
            "min_experience": 1,
            "keywords": ["ios", "android", "mobile app", "app store", "play store"]
        },
        "machine_learning_engineer": {
            "required_skills": ["python", "tensorflow", "pytorch", "docker", "kubernetes"],
            "preferred_skills": ["mlflow", "kubeflow", "aws", "spark", "airflow"],
            "skill_weights": {
                "programming_languages": 0.20,
                "data_science_ml": 0.40,
                "cloud_platforms": 0.20,
                "devops_tools": 0.20
            },
            "min_experience": 2,
            "keywords": ["ml ops", "model deployment", "deep learning", "neural networks", "ai"]
        }
    }
    
    # Experience keywords that might indicate years
    EXPERIENCE_KEYWORDS = [
        "years", "year", "experience", "worked", "developed", "built",
        "designed", "implemented", "managed", "led", "created"
    ]
    
    # Output settings
    OUTPUT_FORMAT = os.getenv("OUTPUT_FORMAT", "json")  # json, csv, dict
    
    @classmethod
    def get_all_skills(cls):
        """Get all predefined skills as a flat list."""
        all_skills = []
        for category in cls.SOFTWARE_SKILLS.values():
            all_skills.extend(category)
        return all_skills
