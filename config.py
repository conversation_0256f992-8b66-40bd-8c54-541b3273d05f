"""
Configuration settings for the resume screening application.
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for resume screening application."""
    
    # AI Model settings
    DEFAULT_MODEL_NAME = "dbmdz/bert-large-cased-finetuned-conll03-english"
    SKILLS_MODEL_NAME = os.getenv("SKILLS_MODEL_NAME", DEFAULT_MODEL_NAME)
    
    # PDF processing settings
    MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", "10"))
    SUPPORTED_FORMATS = [".pdf"]
    
    # Skills extraction settings
    MIN_CONFIDENCE_SCORE = float(os.getenv("MIN_CONFIDENCE_SCORE", "0.7"))
    
    # Common software development skills (can be expanded)
    SOFTWARE_SKILLS = {
        "programming_languages": [
            "python", "java", "javascript", "typescript", "c++", "c#", "go", 
            "rust", "php", "ruby", "swift", "kotlin", "scala", "r", "matlab"
        ],
        "frameworks": [
            "react", "angular", "vue", "django", "flask", "spring", "express",
            "laravel", "rails", "asp.net", "tensorflow", "pytorch", "keras"
        ],
        "databases": [
            "mysql", "postgresql", "mongodb", "redis", "elasticsearch", 
            "oracle", "sqlite", "cassandra", "dynamodb"
        ],
        "cloud_platforms": [
            "aws", "azure", "gcp", "google cloud", "docker", "kubernetes",
            "terraform", "ansible"
        ],
        "tools": [
            "git", "jenkins", "jira", "confluence", "slack", "docker",
            "kubernetes", "linux", "unix", "bash", "powershell"
        ]
    }
    
    # Experience keywords that might indicate years
    EXPERIENCE_KEYWORDS = [
        "years", "year", "experience", "worked", "developed", "built",
        "designed", "implemented", "managed", "led", "created"
    ]
    
    # Output settings
    OUTPUT_FORMAT = os.getenv("OUTPUT_FORMAT", "json")  # json, csv, dict
    
    @classmethod
    def get_all_skills(cls):
        """Get all predefined skills as a flat list."""
        all_skills = []
        for category in cls.SOFTWARE_SKILLS.values():
            all_skills.extend(category)
        return all_skills
