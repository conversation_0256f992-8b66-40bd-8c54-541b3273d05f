"""
Configuration settings for the resume screening application.
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for resume screening application."""

    # AI Model settings - Multiple models for different tasks
    DEFAULT_NER_MODEL = "dbmdz/bert-large-cased-finetuned-conll03-english"
    SKILLS_NER_MODEL = "microsoft/DialoGPT-medium"  # Better for conversational context
    RESUME_SPECIFIC_MODEL = "sentence-transformers/all-MiniLM-L6-v2"  # For semantic similarity

    # Model selection based on task
    SKILLS_MODEL_NAME = os.getenv("SKILLS_MODEL_NAME", DEFAULT_NER_MODEL)
    SEMANTIC_MODEL_NAME = os.getenv("SEMANTIC_MODEL_NAME", RESUME_SPECIFIC_MODEL)
    
    # PDF processing settings
    MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", "10"))
    SUPPORTED_FORMATS = [".pdf"]
    
    # Skills extraction settings
    MIN_CONFIDENCE_SCORE = float(os.getenv("MIN_CONFIDENCE_SCORE", "0.7"))
    
    # Comprehensive software development skills based on industry standards
    # Sources: Stack Overflow 2024 Survey, LinkedIn Jobs, IEEE SWEBOK, O*NET, GitHub Trends
    SOFTWARE_SKILLS = {
        "programming_languages": [
            # Core Languages (Stack Overflow Top 20)
            "javascript", "typescript", "python", "java", "c#", "c++", "php", "go",
            "rust", "kotlin", "swift", "dart", "scala", "ruby", "r", "matlab",

            # Web Languages
            "html", "css", "sass", "scss", "less", "stylus",

            # Systems & Low-Level
            "c", "assembly", "webassembly", "wasm",

            # Functional & Academic
            "haskell", "clojure", "erlang", "elixir", "f#", "ocaml", "lisp",

            # Scripting & Shell
            "bash", "powershell", "perl", "lua", "tcl",

            # Database Languages
            "sql", "plsql", "t-sql", "nosql",

            # Emerging & Specialized
            "solidity", "julia", "zig", "nim", "crystal", "v"
        ],

        "frontend_frameworks": [
            # React Ecosystem
            "react", "next.js", "gatsby", "remix", "react native",

            # Vue Ecosystem
            "vue", "vue.js", "nuxt.js", "quasar",

            # Angular Ecosystem
            "angular", "angularjs", "ionic",

            # Other Modern Frameworks
            "svelte", "sveltekit", "solid.js", "qwik", "lit", "stencil",

            # Traditional & Legacy
            "jquery", "backbone.js", "ember.js", "knockout.js",

            # CSS Frameworks
            "bootstrap", "tailwind css", "bulma", "foundation", "semantic ui",
            "material ui", "ant design", "chakra ui", "styled-components"
        ],

        "backend_frameworks": [
            # Node.js Ecosystem
            "express", "express.js", "fastify", "koa", "nest.js", "adonis.js",

            # Python Frameworks
            "django", "flask", "fastapi", "tornado", "pyramid", "bottle", "falcon",
            "starlette", "sanic", "quart",

            # Java Ecosystem
            "spring", "spring boot", "spring mvc", "spring security", "hibernate",
            "struts", "play framework", "micronaut", "quarkus", "vert.x",

            # .NET Ecosystem
            "asp.net", "asp.net core", "blazor", "entity framework", "web api",

            # PHP Frameworks
            "laravel", "symfony", "codeigniter", "cakephp", "zend", "phalcon",

            # Ruby Frameworks
            "rails", "ruby on rails", "sinatra", "hanami",

            # Go Frameworks
            "gin", "echo", "fiber", "beego", "revel",

            # Rust Frameworks
            "actix", "rocket", "warp", "axum",

            # Other Languages
            "django rest framework", "graphene", "celery"
        ],

        "mobile_development": [
            # Native iOS
            "swift", "objective-c", "swiftui", "uikit", "core data", "xcode",

            # Native Android
            "kotlin", "java", "android sdk", "jetpack compose", "android studio",
            "gradle", "room", "retrofit",

            # Cross-Platform
            "react native", "flutter", "xamarin", "ionic", "cordova", "phonegap",
            "kotlin multiplatform", "unity", "unreal engine",

            # Hybrid & PWA
            "capacitor", "pwa", "workbox", "service workers"
        ],

        "data_science_ai_ml": [
            # Core ML Libraries
            "tensorflow", "pytorch", "keras", "scikit-learn", "xgboost", "lightgbm",
            "catboost", "h2o", "mlflow", "kubeflow", "weights & biases", "wandb",

            # Data Processing
            "pandas", "numpy", "scipy", "dask", "polars", "modin", "ray",

            # Visualization
            "matplotlib", "seaborn", "plotly", "bokeh", "altair", "d3.js",
            "tableau", "power bi", "looker", "grafana",

            # Deep Learning Specialized
            "hugging face", "transformers", "bert", "gpt", "llama", "stable diffusion",
            "opencv", "pillow", "imageio", "albumentations",

            # MLOps & Deployment
            "docker", "kubernetes", "seldon", "bentoml", "torchserve", "tensorflow serving",
            "onnx", "tensorrt", "apache airflow", "prefect", "dagster",

            # Notebooks & IDEs
            "jupyter", "jupyterlab", "google colab", "kaggle", "databricks",
            "anaconda", "conda", "poetry", "pipenv",

            # Big Data Integration
            "spark", "pyspark", "hadoop", "hive", "pig", "kafka", "storm", "flink"
        ],

        "databases": [
            # Relational Databases
            "mysql", "postgresql", "sqlite", "oracle", "sql server", "mariadb",
            "db2", "sybase", "firebird", "cockroachdb", "yugabytedb",

            # NoSQL Document
            "mongodb", "couchdb", "amazon documentdb", "azure cosmos db",

            # NoSQL Key-Value
            "redis", "memcached", "amazon dynamodb", "riak", "leveldb", "rocksdb",

            # NoSQL Column-Family
            "cassandra", "hbase", "amazon simpledb", "google bigtable",

            # Graph Databases
            "neo4j", "amazon neptune", "arangodb", "orientdb", "janusgraph",
            "tigergraph", "dgraph",

            # Time Series
            "influxdb", "timescaledb", "prometheus", "graphite", "opentsdb",

            # Search Engines
            "elasticsearch", "solr", "amazon opensearch", "algolia", "typesense",

            # Data Warehouses
            "snowflake", "amazon redshift", "google bigquery", "azure synapse",
            "databricks", "clickhouse", "apache druid", "vertica",

            # NewSQL
            "spanner", "foundationdb", "voltdb", "nuodb"
        ],

        "cloud_platforms": [
            # Major Cloud Providers
            "aws", "amazon web services", "azure", "microsoft azure", "gcp",
            "google cloud platform", "google cloud", "alibaba cloud", "tencent cloud",
            "oracle cloud", "ibm cloud", "digitalocean", "linode", "vultr",

            # Platform as a Service
            "heroku", "vercel", "netlify", "railway", "render", "fly.io",
            "cloudflare pages", "github pages", "firebase", "supabase",

            # Serverless Platforms
            "aws lambda", "azure functions", "google cloud functions",
            "cloudflare workers", "deno deploy", "edge functions",

            # Container Platforms
            "aws ecs", "aws fargate", "azure container instances", "google cloud run",
            "aws eks", "azure aks", "google gke"
        ],

        "devops_cicd": [
            # Containerization
            "docker", "podman", "containerd", "cri-o", "buildah", "kaniko",

            # Orchestration
            "kubernetes", "k8s", "docker swarm", "nomad", "mesos", "openshift",
            "rancher", "helm", "kustomize", "istio", "linkerd", "envoy",

            # Infrastructure as Code
            "terraform", "ansible", "puppet", "chef", "saltstack", "pulumi",
            "cloudformation", "arm templates", "cdk", "bicep",

            # CI/CD Platforms
            "jenkins", "github actions", "gitlab ci", "azure devops", "circleci",
            "travis ci", "bamboo", "teamcity", "buildkite", "drone", "tekton",
            "argo cd", "flux", "spinnaker",

            # Monitoring & Observability
            "prometheus", "grafana", "elk stack", "elasticsearch", "logstash", "kibana",
            "datadog", "new relic", "splunk", "dynatrace", "honeycomb", "jaeger",
            "zipkin", "opentelemetry", "fluentd", "fluent bit",

            # Security & Compliance
            "vault", "consul", "cert-manager", "falco", "twistlock", "aqua security",
            "snyk", "sonarqube", "checkmarx", "veracode"
        ],

        "development_tools": [
            # Version Control
            "git", "github", "gitlab", "bitbucket", "svn", "mercurial", "perforce",

            # IDEs & Editors
            "vscode", "visual studio", "intellij idea", "pycharm", "webstorm",
            "eclipse", "netbeans", "atom", "sublime text", "vim", "neovim", "emacs",
            "xcode", "android studio",

            # Project Management
            "jira", "confluence", "trello", "asana", "monday.com", "notion",
            "linear", "clickup", "azure boards", "github projects",

            # Communication
            "slack", "microsoft teams", "discord", "zoom", "google meet",

            # API Development
            "postman", "insomnia", "swagger", "openapi", "graphql playground",
            "apollo studio", "rest client",

            # Design & Prototyping
            "figma", "sketch", "adobe xd", "invision", "zeplin", "principle",

            # Package Managers
            "npm", "yarn", "pnpm", "pip", "conda", "poetry", "pipenv", "maven",
            "gradle", "nuget", "composer", "bundler", "cargo", "go mod",

            # Build Tools
            "webpack", "vite", "rollup", "parcel", "esbuild", "turbopack",
            "gulp", "grunt", "make", "cmake", "bazel"
        ],

        "testing_qa": [
            # JavaScript Testing
            "jest", "mocha", "chai", "jasmine", "karma", "cypress", "playwright",
            "puppeteer", "webdriver", "selenium", "testcafe", "nightwatch",

            # Python Testing
            "pytest", "unittest", "nose", "doctest", "hypothesis", "factory boy",

            # Java Testing
            "junit", "testng", "mockito", "spring test", "cucumber", "rest assured",

            # .NET Testing
            "nunit", "xunit", "mstest", "specflow",

            # Mobile Testing
            "espresso", "ui automator", "xctest", "earl grey", "detox", "appium",

            # Performance Testing
            "jmeter", "gatling", "k6", "locust", "artillery", "blazemeter",

            # API Testing
            "postman", "newman", "rest assured", "supertest", "frisby",

            # BDD & Acceptance Testing
            "cucumber", "gherkin", "specflow", "behave", "lettuce"
        ],

        "big_data_analytics": [
            # Processing Frameworks
            "apache spark", "hadoop", "mapreduce", "apache flink", "apache storm",
            "apache beam", "apache samza", "apache nifi",

            # Streaming
            "apache kafka", "apache pulsar", "amazon kinesis", "azure event hubs",
            "google pub/sub", "rabbitmq", "apache activemq", "nats",

            # Storage
            "hdfs", "amazon s3", "azure blob storage", "google cloud storage",
            "apache hbase", "apache cassandra", "apache parquet", "apache avro",
            "apache orc", "delta lake", "apache iceberg", "apache hudi",

            # Query Engines
            "apache hive", "apache pig", "presto", "trino", "apache drill",
            "apache impala", "spark sql", "dremio",

            # Workflow Management
            "apache airflow", "prefect", "dagster", "luigi", "azkaban", "oozie"
        ],

        "cybersecurity": [
            # Security Frameworks
            "owasp", "nist", "iso 27001", "pci dss", "hipaa", "gdpr", "sox",

            # Security Tools
            "burp suite", "owasp zap", "nmap", "wireshark", "metasploit",
            "nessus", "qualys", "rapid7", "checkmarx", "veracode", "snyk",
            "sonarqube", "fortify", "bandit", "semgrep",

            # Authentication & Authorization
            "oauth", "oauth2", "openid connect", "saml", "jwt", "ldap",
            "active directory", "okta", "auth0", "keycloak", "ping identity",

            # Encryption & PKI
            "ssl", "tls", "https", "pki", "certificates", "vault", "kms",
            "pgp", "gpg", "aes", "rsa", "elliptic curve"
        ],

        "emerging_technologies": [
            # Blockchain & Web3
            "blockchain", "ethereum", "bitcoin", "solidity", "web3", "defi",
            "nft", "smart contracts", "metamask", "truffle", "hardhat", "ganache",

            # AR/VR
            "unity", "unreal engine", "arkit", "arcore", "oculus", "hololens",
            "webxr", "a-frame", "three.js",

            # IoT
            "arduino", "raspberry pi", "mqtt", "coap", "zigbee", "bluetooth",
            "wifi", "lora", "sigfox", "nb-iot",

            # Quantum Computing
            "qiskit", "cirq", "quantum", "q#", "quantum computing",

            # Edge Computing
            "edge computing", "fog computing", "aws greengrass", "azure iot edge",

            # Low-Code/No-Code
            "zapier", "microsoft power platform", "salesforce", "airtable",
            "bubble", "webflow", "retool", "appsmith"
        ],

        "soft_skills_methodologies": [
            # Agile & Project Management
            "agile", "scrum", "kanban", "lean", "safe", "waterfall", "prince2",
            "pmp", "csm", "psm", "jira", "azure boards", "monday.com",

            # Development Practices
            "tdd", "test driven development", "bdd", "behavior driven development",
            "ddd", "domain driven design", "clean code", "solid principles",
            "design patterns", "refactoring", "pair programming", "code review",

            # Architecture & Design
            "microservices", "monolith", "soa", "event driven architecture",
            "cqrs", "event sourcing", "hexagonal architecture", "clean architecture",
            "mvc", "mvp", "mvvm", "rest", "graphql", "grpc", "soap",

            # Communication & Leadership
            "technical writing", "documentation", "mentoring", "team leadership",
            "stakeholder management", "requirements gathering", "user stories",
            "acceptance criteria"
        ]
    }

    # Role-specific skill requirements and weights based on industry analysis
    ROLE_SKILL_MAPPING = {
        "data_scientist": {
            "required_skills": ["python", "r", "sql", "pandas", "numpy", "scikit-learn", "jupyter"],
            "preferred_skills": ["tensorflow", "pytorch", "matplotlib", "seaborn", "plotly", "spark", "aws"],
            "skill_weights": {
                "programming_languages": 0.25,
                "data_science_ai_ml": 0.35,
                "databases": 0.15,
                "cloud_platforms": 0.10,
                "big_data_analytics": 0.15
            },
            "min_experience": 2,
            "keywords": ["machine learning", "data analysis", "statistics", "modeling", "algorithms", "predictive analytics"]
        },

        "data_engineer": {
            "required_skills": ["python", "sql", "spark", "airflow", "kafka", "hadoop", "aws"],
            "preferred_skills": ["docker", "kubernetes", "terraform", "snowflake", "redshift", "bigquery"],
            "skill_weights": {
                "programming_languages": 0.20,
                "databases": 0.25,
                "big_data_analytics": 0.25,
                "cloud_platforms": 0.20,
                "devops_cicd": 0.10
            },
            "min_experience": 2,
            "keywords": ["etl", "data pipeline", "data warehouse", "streaming", "batch processing", "data lake"]
        },

        "full_stack_developer": {
            "required_skills": ["javascript", "typescript", "html", "css", "react", "node.js", "express"],
            "preferred_skills": ["next.js", "docker", "aws", "mongodb", "postgresql", "git"],
            "skill_weights": {
                "programming_languages": 0.25,
                "frontend_frameworks": 0.25,
                "backend_frameworks": 0.20,
                "databases": 0.15,
                "cloud_platforms": 0.15
            },
            "min_experience": 1,
            "keywords": ["frontend", "backend", "api", "responsive design", "user interface", "full stack"]
        },

        "backend_developer": {
            "required_skills": ["java", "python", "sql", "spring", "rest", "api", "microservices"],
            "preferred_skills": ["docker", "kubernetes", "aws", "redis", "postgresql", "mongodb"],
            "skill_weights": {
                "programming_languages": 0.30,
                "backend_frameworks": 0.25,
                "databases": 0.20,
                "cloud_platforms": 0.15,
                "devops_cicd": 0.10
            },
            "min_experience": 1,
            "keywords": ["api", "microservices", "server", "database", "scalability", "backend"]
        },

        "frontend_developer": {
            "required_skills": ["javascript", "typescript", "html", "css", "react", "responsive design"],
            "preferred_skills": ["vue", "angular", "sass", "webpack", "figma", "tailwind css"],
            "skill_weights": {
                "programming_languages": 0.25,
                "frontend_frameworks": 0.40,
                "development_tools": 0.20,
                "testing_qa": 0.15
            },
            "min_experience": 1,
            "keywords": ["ui", "ux", "responsive", "component", "user experience", "frontend"]
        },

        "devops_engineer": {
            "required_skills": ["docker", "kubernetes", "aws", "terraform", "jenkins", "ci/cd"],
            "preferred_skills": ["ansible", "prometheus", "grafana", "helm", "istio", "vault"],
            "skill_weights": {
                "devops_cicd": 0.40,
                "cloud_platforms": 0.30,
                "programming_languages": 0.15,
                "cybersecurity": 0.15
            },
            "min_experience": 2,
            "keywords": ["ci/cd", "infrastructure", "automation", "monitoring", "deployment", "devops"]
        },

        "mobile_developer": {
            "required_skills": ["swift", "kotlin", "react native", "flutter", "ios", "android"],
            "preferred_skills": ["swiftui", "jetpack compose", "firebase", "xcode", "android studio"],
            "skill_weights": {
                "programming_languages": 0.30,
                "mobile_development": 0.40,
                "development_tools": 0.20,
                "cloud_platforms": 0.10
            },
            "min_experience": 1,
            "keywords": ["ios", "android", "mobile app", "app store", "play store", "mobile"]
        },

        "machine_learning_engineer": {
            "required_skills": ["python", "tensorflow", "pytorch", "docker", "kubernetes", "mlflow"],
            "preferred_skills": ["kubeflow", "aws", "spark", "airflow", "mlops", "model deployment"],
            "skill_weights": {
                "programming_languages": 0.20,
                "data_science_ai_ml": 0.40,
                "cloud_platforms": 0.20,
                "devops_cicd": 0.20
            },
            "min_experience": 2,
            "keywords": ["mlops", "model deployment", "deep learning", "neural networks", "ai", "machine learning"]
        },

        "security_engineer": {
            "required_skills": ["cybersecurity", "owasp", "penetration testing", "vulnerability assessment"],
            "preferred_skills": ["burp suite", "nmap", "wireshark", "aws", "terraform", "vault"],
            "skill_weights": {
                "cybersecurity": 0.50,
                "cloud_platforms": 0.20,
                "devops_cicd": 0.15,
                "programming_languages": 0.15
            },
            "min_experience": 2,
            "keywords": ["security", "penetration testing", "vulnerability", "compliance", "encryption"]
        },

        "cloud_architect": {
            "required_skills": ["aws", "azure", "gcp", "terraform", "kubernetes", "microservices"],
            "preferred_skills": ["serverless", "lambda", "docker", "helm", "istio", "vault"],
            "skill_weights": {
                "cloud_platforms": 0.40,
                "devops_cicd": 0.25,
                "backend_frameworks": 0.20,
                "cybersecurity": 0.15
            },
            "min_experience": 3,
            "keywords": ["cloud architecture", "scalability", "high availability", "disaster recovery", "cost optimization"]
        },

        "qa_engineer": {
            "required_skills": ["selenium", "cypress", "jest", "junit", "test automation", "api testing"],
            "preferred_skills": ["playwright", "postman", "jmeter", "cucumber", "docker", "ci/cd"],
            "skill_weights": {
                "testing_qa": 0.50,
                "programming_languages": 0.20,
                "development_tools": 0.15,
                "devops_cicd": 0.15
            },
            "min_experience": 1,
            "keywords": ["test automation", "quality assurance", "testing", "bug tracking", "test planning"]
        },

        "blockchain_developer": {
            "required_skills": ["solidity", "ethereum", "web3", "smart contracts", "blockchain"],
            "preferred_skills": ["truffle", "hardhat", "metamask", "defi", "nft", "javascript"],
            "skill_weights": {
                "emerging_technologies": 0.40,
                "programming_languages": 0.30,
                "frontend_frameworks": 0.15,
                "cybersecurity": 0.15
            },
            "min_experience": 1,
            "keywords": ["blockchain", "cryptocurrency", "smart contracts", "defi", "web3", "dapp"]
        }
    }
    
    # Experience keywords that might indicate years
    EXPERIENCE_KEYWORDS = [
        "years", "year", "experience", "worked", "developed", "built",
        "designed", "implemented", "managed", "led", "created"
    ]
    
    # Output settings
    OUTPUT_FORMAT = os.getenv("OUTPUT_FORMAT", "json")  # json, csv, dict
    
    @classmethod
    def get_all_skills(cls):
        """Get all predefined skills as a flat list."""
        all_skills = []
        for category in cls.SOFTWARE_SKILLS.values():
            all_skills.extend(category)
        return all_skills
