# Flexible requirements for resume screening application
# This version uses compatible version ranges to avoid conflicts

# Core PDF processing
PyPDF2>=3.0.0
pdfplumber>=0.9.0
pymupdf>=1.23.0

# AI and ML libraries
transformers>=4.30.0
torch>=2.0.0
sentence-transformers>=2.2.0
huggingface-hub>=0.16.0

# Data processing
pandas>=2.0.0
numpy>=1.24.0
scikit-learn>=1.3.0

# NLP libraries
spacy>=3.7.0
nltk>=3.8.0
regex>=2023.0.0

# Configuration and utilities
python-dotenv>=1.0.0
requests>=2.31.0

# OCR and image processing (optional)
pytesseract>=0.3.0
Pillow>=10.0.0
pdf2image>=1.16.0

# Alternative PDF parsers (optional)
pdfminer.six>=20221105

# Text analysis
textstat>=0.7.0

# Testing
pytest>=7.4.0
pytest-cov>=4.1.0

# Optional: Advanced features
# Uncomment if needed
# camelot-py[cv]>=0.11.0
# wordcloud>=1.9.0
# openai>=1.0.0
# anthropic>=0.8.0
