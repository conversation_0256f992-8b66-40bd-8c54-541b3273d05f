# Resume Screening Application

A Python application that analyzes PDF resumes using AI models to extract skills and years of experience, specifically designed for software development roles.

## Features

- **PDF Text Extraction**: Supports PDF resume parsing with multiple extraction methods
- **AI-Powered Analysis**: Uses Hugging Face transformers for Named Entity Recognition (NER)
- **Skills Extraction**: Identifies programming languages, frameworks, databases, cloud platforms, and tools
- **Experience Analysis**: Extracts years of experience for each identified skill
- **Categorization**: Organizes skills into predefined categories
- **Multiple Output Formats**: Supports JSON and CSV output
- **Batch Processing**: Can process multiple resumes at once

## Installation

1. Clone or download this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

3. Download the spaCy English model (optional, for enhanced NLP):

```bash
python -m spacy download en_core_web_sm
```

## Usage

### Basic Usage

Process a single resume:

```bash
python main.py path/to/resume.pdf
```

Process multiple resumes:

```bash
python main.py resume1.pdf resume2.pdf resume3.pdf
```

### Advanced Options

```bash
# Specify output file and format
python main.py resume.pdf -o results.json -f json

# Use a different AI model
python main.py resume.pdf -m "microsoft/DialoGPT-medium"

# Enable verbose logging
python main.py resume.pdf -v

# Save results as CSV
python main.py resume.pdf -f csv -o results.csv

# List all predefined skills
python main.py --list-skills
```

### Command Line Arguments

- `files`: Path(s) to PDF resume file(s) to analyze
- `-o, --output`: Output file path for results (default: results.json)
- `-f, --format`: Output format - json or csv (default: json)
- `-m, --model`: Hugging Face model name to use for analysis
- `-v, --verbose`: Enable verbose logging
- `--list-skills`: List all predefined skills and exit

## Configuration

The application can be configured through the `config.py` file or environment variables:

### Environment Variables

- `SKILLS_MODEL_NAME`: Hugging Face model name for NER
- `MAX_FILE_SIZE_MB`: Maximum PDF file size in MB (default: 10)
- `MIN_CONFIDENCE_SCORE`: Minimum confidence score for entity extraction (default: 0.7)
- `OUTPUT_FORMAT`: Default output format (default: json)

### Predefined Skills Categories

The application recognizes skills in the following categories:

- **Programming Languages**: Python, Java, JavaScript, TypeScript, C++, C#, Go, Rust, PHP, Ruby, Swift, Kotlin, Scala, R, MATLAB
- **Frameworks**: React, Angular, Vue, Django, Flask, Spring, Express, Laravel, Rails, ASP.NET, TensorFlow, PyTorch, Keras
- **Databases**: MySQL, PostgreSQL, MongoDB, Redis, Elasticsearch, Oracle, SQLite, Cassandra, DynamoDB
- **Cloud Platforms**: AWS, Azure, GCP, Google Cloud, Docker, Kubernetes, Terraform, Ansible
- **Tools**: Git, Jenkins, JIRA, Confluence, Slack, Docker, Kubernetes, Linux, Unix, Bash, PowerShell

## Output Format

### JSON Output

```json
{
  "file_path": "resume.pdf",
  "timestamp": "2024-01-01T12:00:00",
  "success": true,
  "categorized_skills": {
    "programming_languages": [
      {
        "skill": "Python",
        "years": 5,
        "confidence": 0.95,
        "context": "5 years of Python development experience"
      }
    ]
  },
  "summary": {
    "total_skills_found": 15,
    "strongest_category": "programming_languages",
    "total_experience_years": 8
  }
}
```

### CSV Output

The CSV format flattens the data for easy analysis in spreadsheet applications, including:
- Basic information (file path, success status)
- Skill counts by category
- Top 5 skills with years and confidence scores
- Summary statistics

## Architecture

The application consists of several modular components:

1. **PDFReader** (`src/pdf_reader.py`): Handles PDF text extraction
2. **AIAnalyzer** (`src/ai_analyzer.py`): Manages AI model integration and entity extraction
3. **SkillsExtractor** (`src/skills_extractor.py`): Processes and categorizes extracted skills
4. **ResumeScreener** (`src/resume_screener.py`): Main orchestrator class
5. **Configuration** (`config.py`): Centralized configuration management

## AI Models

The application uses Hugging Face transformers for NER. The default model is:
- `dbmdz/bert-large-cased-finetuned-conll03-english`

You can specify different models using the `-m` parameter. Recommended models:
- `microsoft/DialoGPT-medium`
- `bert-base-cased`
- `distilbert-base-cased`

## Logging

The application generates detailed logs in `resume_screening.log` and console output. Use the `-v` flag for verbose logging.

## Error Handling

The application includes comprehensive error handling for:
- Invalid file formats
- Corrupted PDF files
- AI model loading failures
- Network connectivity issues

## Limitations

- Currently supports only PDF format (more formats planned)
- Optimized for software development resumes
- Requires internet connection for initial model download
- Performance depends on PDF quality and structure

## Future Enhancements

- Support for additional file formats (DOCX, TXT)
- Custom model fine-tuning for better accuracy
- Web interface
- Database integration
- Resume ranking and comparison features
- Integration with ATS systems

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
