"""
Advanced PDF parser with multiple extraction methods including OCR and AI.
"""
import os
import logging
import re
from typing import Optional, Dict, Any, List
import tempfile

# Core PDF libraries
import PyPDF2
import pdfplumber
import fitz  # PyMuPDF

# OCR libraries
try:
    import pytesseract
    from pdf2image import convert_from_path
    from PIL import Image
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    logging.warning("OCR libraries not available. Install pytesseract, pdf2image, and Pillow for OCR support.")

# Alternative PDF parsers
try:
    from pdfminer.high_level import extract_text as pdfminer_extract
    from pdfminer.layout import LAParams
    PDFMINER_AVAILABLE = True
except ImportError:
    PDFMINER_AVAILABLE = False

from config import Config

logger = logging.getLogger(__name__)

class AdvancedPDFParser:
    """Advanced PDF parser with multiple extraction methods."""
    
    def __init__(self):
        self.config = Config()
        
        # Configure OCR if available
        if OCR_AVAILABLE:
            # Try to configure tesseract path (common locations)
            tesseract_paths = [
                '/usr/bin/tesseract',
                '/usr/local/bin/tesseract',
                '/opt/homebrew/bin/tesseract',
                'C:\\Program Files\\Tesseract-OCR\\tesseract.exe',
                'C:\\Program Files (x86)\\Tesseract-OCR\\tesseract.exe'
            ]
            
            for path in tesseract_paths:
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    break
    
    def extract_text_comprehensive(self, file_path: str) -> Dict[str, Any]:
        """
        Extract text using multiple methods and return the best result.
        
        Args:
            file_path (str): Path to the PDF file
            
        Returns:
            Dict[str, Any]: Comprehensive extraction results
        """
        results = {
            'success': False,
            'text': '',
            'method_used': None,
            'all_methods': {},
            'confidence_score': 0.0,
            'error': None
        }
        
        extraction_methods = [
            ('pdfplumber_enhanced', self._extract_with_pdfplumber_enhanced),
            ('pymupdf', self._extract_with_pymupdf),
            ('pdfminer', self._extract_with_pdfminer),
            ('pypdf2_enhanced', self._extract_with_pypdf2_enhanced),
            ('ocr', self._extract_with_ocr),
        ]
        
        best_text = ""
        best_score = 0.0
        best_method = None
        
        for method_name, method_func in extraction_methods:
            try:
                logger.info(f"Trying extraction method: {method_name}")
                method_result = method_func(file_path)
                
                if method_result and method_result.get('text'):
                    text = method_result['text']
                    score = self._calculate_text_quality_score(text)
                    
                    results['all_methods'][method_name] = {
                        'text': text,
                        'score': score,
                        'length': len(text),
                        'success': True
                    }
                    
                    logger.info(f"{method_name}: extracted {len(text)} chars, score: {score:.2f}")
                    
                    if score > best_score:
                        best_text = text
                        best_score = score
                        best_method = method_name
                else:
                    results['all_methods'][method_name] = {
                        'success': False,
                        'error': method_result.get('error', 'Unknown error') if method_result else 'No result'
                    }
                    
            except Exception as e:
                logger.error(f"Error with {method_name}: {str(e)}")
                results['all_methods'][method_name] = {
                    'success': False,
                    'error': str(e)
                }
        
        if best_text:
            results.update({
                'success': True,
                'text': self._post_process_text(best_text),
                'method_used': best_method,
                'confidence_score': best_score
            })
        else:
            results['error'] = "All extraction methods failed"
        
        return results
    
    def _extract_with_pdfplumber_enhanced(self, file_path: str) -> Dict[str, Any]:
        """Enhanced pdfplumber extraction with multiple strategies."""
        try:
            with pdfplumber.open(file_path) as pdf:
                text_parts = []
                
                for page in pdf.pages:
                    # Strategy 1: Standard extraction
                    page_text = page.extract_text()
                    
                    # Strategy 2: With custom settings if standard fails
                    if not page_text or len(page_text.strip()) < 20:
                        page_text = page.extract_text(
                            x_tolerance=2,
                            y_tolerance=2,
                            layout=True,
                            x_density=7.25,
                            y_density=13,
                            keep_blank_chars=True
                        )
                    
                    # Strategy 3: Extract words and reconstruct
                    if not page_text or len(page_text.strip()) < 20:
                        words = page.extract_words()
                        if words:
                            # Group words by lines based on y-coordinate
                            lines = {}
                            for word in words:
                                y = round(word['top'])
                                if y not in lines:
                                    lines[y] = []
                                lines[y].append(word)
                            
                            # Sort lines and reconstruct text
                            sorted_lines = sorted(lines.keys())
                            line_texts = []
                            for y in sorted_lines:
                                line_words = sorted(lines[y], key=lambda w: w['x0'])
                                line_text = ' '.join(word['text'] for word in line_words)
                                line_texts.append(line_text)
                            
                            page_text = '\n'.join(line_texts)
                    
                    # Extract tables
                    tables = page.extract_tables()
                    if tables:
                        for table in tables:
                            for row in table:
                                if row:
                                    table_text = ' | '.join([str(cell) if cell else '' for cell in row])
                                    page_text += '\n' + table_text
                    
                    if page_text:
                        text_parts.append(page_text)
                
                return {
                    'text': '\n\n'.join(text_parts),
                    'success': True
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _extract_with_pymupdf(self, file_path: str) -> Dict[str, Any]:
        """Extract text using PyMuPDF (fitz)."""
        try:
            doc = fitz.open(file_path)
            text_parts = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # Method 1: Standard text extraction
                text = page.get_text()
                
                # Method 2: Extract with layout preservation
                if not text or len(text.strip()) < 20:
                    text = page.get_text("text", sort=True)
                
                # Method 3: Extract blocks for better structure
                if not text or len(text.strip()) < 20:
                    blocks = page.get_text("dict")
                    text_blocks = []
                    for block in blocks.get("blocks", []):
                        if "lines" in block:
                            for line in block["lines"]:
                                line_text = ""
                                for span in line.get("spans", []):
                                    line_text += span.get("text", "")
                                if line_text.strip():
                                    text_blocks.append(line_text)
                    text = '\n'.join(text_blocks)
                
                if text:
                    text_parts.append(text)
            
            doc.close()
            
            return {
                'text': '\n\n'.join(text_parts),
                'success': True
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _extract_with_pdfminer(self, file_path: str) -> Dict[str, Any]:
        """Extract text using pdfminer."""
        if not PDFMINER_AVAILABLE:
            return {'success': False, 'error': 'pdfminer not available'}
        
        try:
            # Configure layout analysis parameters
            laparams = LAParams(
                line_margin=0.5,
                word_margin=0.1,
                char_margin=2.0,
                boxes_flow=0.5,
                all_texts=False
            )
            
            text = pdfminer_extract(file_path, laparams=laparams)
            
            return {
                'text': text,
                'success': True
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _extract_with_pypdf2_enhanced(self, file_path: str) -> Dict[str, Any]:
        """Enhanced PyPDF2 extraction."""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_parts = []
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()
                    
                    if page_text:
                        text_parts.append(page_text)
                
                return {
                    'text': '\n\n'.join(text_parts),
                    'success': True
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _extract_with_ocr(self, file_path: str) -> Dict[str, Any]:
        """Extract text using OCR as fallback."""
        if not OCR_AVAILABLE:
            return {'success': False, 'error': 'OCR libraries not available'}
        
        try:
            # Convert PDF to images
            with tempfile.TemporaryDirectory() as temp_dir:
                images = convert_from_path(file_path, dpi=300, output_folder=temp_dir)
                
                text_parts = []
                for i, image in enumerate(images):
                    # Use OCR to extract text
                    ocr_text = pytesseract.image_to_string(
                        image,
                        config='--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@#$%^&*()_+-=[]{}|;:,.<>?/~` '
                    )
                    
                    if ocr_text.strip():
                        text_parts.append(ocr_text)
                
                return {
                    'text': '\n\n'.join(text_parts),
                    'success': True
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _calculate_text_quality_score(self, text: str) -> float:
        """
        Calculate a quality score for extracted text.

        Args:
            text (str): Extracted text

        Returns:
            float: Quality score (0-100)
        """
        if not text or len(text.strip()) < 10:
            return 0.0

        score = 0.0

        # Length score (longer is generally better, up to a point)
        length_score = min(len(text) / 1000, 50)  # Max 50 points for length
        score += length_score

        # Email detection score
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        if re.search(email_pattern, text):
            score += 15

        # Phone detection score
        phone_pattern = r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
        if re.search(phone_pattern, text):
            score += 10

        # Common resume keywords score
        resume_keywords = [
            'experience', 'education', 'skills', 'work', 'employment',
            'university', 'college', 'degree', 'certification', 'project',
            'programming', 'software', 'developer', 'engineer', 'manager'
        ]

        keyword_count = sum(1 for keyword in resume_keywords if keyword.lower() in text.lower())
        score += min(keyword_count * 2, 20)  # Max 20 points for keywords

        # Readability score (penalize too much garbled text)
        words = text.split()
        if words:
            # Check for reasonable word length distribution
            avg_word_length = sum(len(word) for word in words) / len(words)
            if 3 <= avg_word_length <= 8:  # Reasonable average word length
                score += 5

            # Check for reasonable character distribution
            alpha_ratio = sum(1 for char in text if char.isalpha()) / len(text)
            if alpha_ratio > 0.6:  # At least 60% alphabetic characters
                score += 10

        return min(score, 100)  # Cap at 100

    def _post_process_text(self, text: str) -> str:
        """
        Post-process extracted text to improve quality.

        Args:
            text (str): Raw extracted text

        Returns:
            str: Processed text
        """
        if not text:
            return ""

        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Fix broken words (common in PDF extraction)
        text = re.sub(r'([a-z])([A-Z])', r'\1 \2', text)

        # Fix broken email addresses
        text = re.sub(r'([a-zA-Z0-9._%+-]+)\s*@\s*([a-zA-Z0-9.-]+)\s*\.\s*([a-zA-Z]{2,})', r'\1@\2.\3', text)

        # Fix broken phone numbers
        text = re.sub(r'(\d{3})\s*[-.]?\s*(\d{3})\s*[-.]?\s*(\d{4})', r'\1-\2-\3', text)

        # Fix broken URLs
        text = re.sub(r'(https?://)\s*([^\s]+)', r'\1\2', text)
        text = re.sub(r'(www\.)\s*([^\s]+)', r'\1\2', text)

        # Fix LinkedIn/GitHub URLs
        text = re.sub(r'linkedin\s*\.\s*com\s*/\s*in\s*/\s*([^\s]+)', r'linkedin.com/in/\1', text, flags=re.IGNORECASE)
        text = re.sub(r'github\s*\.\s*com\s*/\s*([^\s]+)', r'github.com/\1', text, flags=re.IGNORECASE)

        # Fix common PDF extraction artifacts
        text = re.sub(r'([a-z])([A-Z])', r'\1 \2', text)  # Split camelCase
        text = re.sub(r'([.!?])\s*([A-Z])', r'\1\n\2', text)  # Add line breaks after sentences

        # Clean up multiple consecutive newlines
        text = re.sub(r'\n\s*\n', '\n\n', text)

        # Remove leading/trailing whitespace from each line
        lines = [line.strip() for line in text.split('\n')]
        text = '\n'.join(line for line in lines if line)

        return text.strip()
