"""
Main resume screener class that orchestrates the entire resume analysis process.
"""
import logging
import json
from typing import Dict, Any, Optional
from datetime import datetime

from .pdf_reader import PDFReader
from .ai_analyzer import AIAnalyzer
from .skills_extractor import SkillsExtractor
from config import Config

logger = logging.getLogger(__name__)

class ResumeScreener:
    """Main class for screening resumes and extracting skills/experience."""
    
    def __init__(self, model_name: Optional[str] = None):
        """
        Initialize the resume screener.
        
        Args:
            model_name (str, optional): Name of the AI model to use
        """
        self.config = Config()
        self.pdf_reader = PDFReader()
        self.ai_analyzer = AIAnalyzer(model_name)
        self.skills_extractor = SkillsExtractor()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def process_resume(self, file_path: str) -> Dict[str, Any]:
        """
        Process a resume file and extract skills and experience.
        
        Args:
            file_path (str): Path to the resume PDF file
            
        Returns:
            Dict[str, Any]: Complete analysis results
        """
        logger.info(f"Starting resume analysis for: {file_path}")
        
        result = {
            'file_path': file_path,
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'error': None,
            'extraction_method': None,
            'raw_text': None,
            'entities': [],
            'skills': {},
            'categorized_skills': {},
            'additional_info': {},
            'summary': {}
        }
        
        try:
            # Step 1: Extract text from PDF
            logger.info("Extracting text from PDF...")
            pdf_result = self.pdf_reader.extract_text(file_path)
            
            if not pdf_result['success']:
                result['error'] = f"PDF extraction failed: {pdf_result.get('error', 'Unknown error')}"
                return result
            
            result['raw_text'] = pdf_result['text']
            result['extraction_method'] = pdf_result['method_used']
            
            # Step 2: Analyze text with AI model
            logger.info("Analyzing text with AI model...")
            entities = self.ai_analyzer.extract_entities(pdf_result['text'])
            result['entities'] = entities
            
            # Step 3: Extract skills from entities
            logger.info("Extracting skills...")
            skills = self.ai_analyzer.extract_skills_from_entities(entities, pdf_result['text'])
            
            # Step 4: Extract experience years
            logger.info("Extracting experience years...")
            skills_with_experience = self.ai_analyzer.extract_experience_years(pdf_result['text'], skills)
            result['skills'] = skills_with_experience
            
            # Step 5: Categorize skills
            logger.info("Categorizing skills...")
            categorized_skills = self.skills_extractor.categorize_skills(skills_with_experience)
            result['categorized_skills'] = categorized_skills
            
            # Step 6: Extract additional information
            logger.info("Extracting additional information...")
            additional_info = self.skills_extractor.extract_additional_info(pdf_result['text'])
            result['additional_info'] = additional_info
            
            # Step 7: Generate summary
            logger.info("Generating summary...")
            summary = self.skills_extractor.generate_summary(categorized_skills, additional_info)
            result['summary'] = summary
            
            result['success'] = True
            logger.info("Resume analysis completed successfully")
            
        except Exception as e:
            error_msg = f"Error during resume processing: {str(e)}"
            logger.error(error_msg)
            result['error'] = error_msg
        
        return result
    
    def process_multiple_resumes(self, file_paths: list) -> Dict[str, Any]:
        """
        Process multiple resume files.
        
        Args:
            file_paths (list): List of paths to resume PDF files
            
        Returns:
            Dict[str, Any]: Results for all processed resumes
        """
        results = {
            'total_files': len(file_paths),
            'successful': 0,
            'failed': 0,
            'results': []
        }
        
        for file_path in file_paths:
            result = self.process_resume(file_path)
            results['results'].append(result)
            
            if result['success']:
                results['successful'] += 1
            else:
                results['failed'] += 1
        
        return results
    
    def save_results(self, results: Dict[str, Any], output_path: str, format: str = 'json') -> bool:
        """
        Save analysis results to file.
        
        Args:
            results (Dict[str, Any]): Analysis results
            output_path (str): Path to save the results
            format (str): Output format ('json' or 'csv')
            
        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            if format.lower() == 'json':
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2, ensure_ascii=False)
            
            elif format.lower() == 'csv':
                import pandas as pd
                
                # Flatten results for CSV format
                flattened_data = []
                
                if 'results' in results:  # Multiple resumes
                    for result in results['results']:
                        flattened_data.append(self._flatten_result(result))
                else:  # Single resume
                    flattened_data.append(self._flatten_result(results))
                
                df = pd.DataFrame(flattened_data)
                df.to_csv(output_path, index=False)
            
            else:
                logger.error(f"Unsupported output format: {format}")
                return False
            
            logger.info(f"Results saved to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving results: {str(e)}")
            return False
    
    def _flatten_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Flatten nested result structure for CSV export.
        
        Args:
            result (Dict[str, Any]): Single resume analysis result
            
        Returns:
            Dict[str, Any]: Flattened result
        """
        flattened = {
            'file_path': result.get('file_path', ''),
            'success': result.get('success', False),
            'error': result.get('error', ''),
            'total_skills': result.get('summary', {}).get('total_skills_found', 0),
            'total_experience_years': result.get('additional_info', {}).get('total_experience'),
            'strongest_category': result.get('summary', {}).get('strongest_category', ''),
            'has_contact_info': result.get('summary', {}).get('has_contact_info', False),
            'education_count': result.get('summary', {}).get('education_count', 0),
            'certifications_count': result.get('summary', {}).get('certifications_count', 0)
        }
        
        # Add category counts
        skill_counts = result.get('summary', {}).get('skills_by_category', {})
        for category, count in skill_counts.items():
            flattened[f'{category}_skills_count'] = count
        
        # Add top 5 skills
        top_skills = result.get('summary', {}).get('top_skills', [])[:5]
        for i, skill in enumerate(top_skills):
            flattened[f'top_skill_{i+1}'] = skill.get('skill', '')
            flattened[f'top_skill_{i+1}_years'] = skill.get('years')
            flattened[f'top_skill_{i+1}_confidence'] = skill.get('confidence', 0.0)
        
        return flattened
