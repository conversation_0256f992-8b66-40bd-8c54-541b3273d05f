"""
Skills extractor module for processing and structuring extracted skills and experience.
"""
import logging
import re
from typing import Dict, List, Any, Optional
from collections import defaultdict
from config import Config

logger = logging.getLogger(__name__)

class SkillsExtractor:
    """Class for extracting and structuring skills and experience from resume text."""
    
    def __init__(self):
        self.config = Config()
    
    def categorize_skills(self, skills_data: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Categorize skills into predefined categories.
        
        Args:
            skills_data (Dict[str, Any]): Skills with experience data
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: Categorized skills
        """
        categorized = defaultdict(list)
        
        for skill, data in skills_data.items():
            skill_lower = skill.lower()
            category_found = False
            
            # Check each category
            for category, skill_list in self.config.SOFTWARE_SKILLS.items():
                if any(s.lower() == skill_lower for s in skill_list):
                    categorized[category].append({
                        'skill': skill,
                        'years': data.get('years'),
                        'confidence': data.get('confidence', 0.0),
                        'context': data.get('context', ''),
                        'source': data.get('source', '')
                    })
                    category_found = True
                    break
            
            # If no category found, put in 'other'
            if not category_found:
                categorized['other'].append({
                    'skill': skill,
                    'years': data.get('years'),
                    'confidence': data.get('confidence', 0.0),
                    'context': data.get('context', ''),
                    'source': data.get('source', '')
                })
        
        return dict(categorized)
    
    def extract_additional_info(self, text: str) -> Dict[str, Any]:
        """
        Extract additional information from resume text.
        
        Args:
            text (str): Resume text
            
        Returns:
            Dict[str, Any]: Additional extracted information
        """
        info = {
            'total_experience': None,
            'education': [],
            'certifications': [],
            'projects': [],
            'contact_info': {}
        }
        
        # Extract total years of experience
        total_exp_patterns = [
            r'(\d+)\s*(?:years?|yrs?)\s*(?:of\s*)?(?:total\s*)?(?:professional\s*)?experience',
            r'(?:total\s*)?(?:professional\s*)?experience\s*(?:of\s*)?(\d+)\s*(?:years?|yrs?)',
            r'(\d+)\+?\s*(?:years?|yrs?)\s*(?:in\s*)?(?:software\s*)?(?:development|engineering|programming)'
        ]
        
        for pattern in total_exp_patterns:
            match = re.search(pattern, text.lower())
            if match:
                info['total_experience'] = int(match.group(1))
                break
        
        # Extract education
        education_patterns = [
            r'(?:bachelor|master|phd|doctorate|degree)\s*(?:of\s*)?(?:science|arts|engineering)?\s*(?:in\s*)?([^.\n]+)',
            r'(b\.?s\.?|m\.?s\.?|ph\.?d\.?|b\.?a\.?|m\.?a\.?)\s*(?:in\s*)?([^.\n]+)',
        ]
        
        for pattern in education_patterns:
            matches = re.finditer(pattern, text.lower())
            for match in matches:
                education_text = match.group(0).strip()
                if len(education_text) > 5:  # Filter out very short matches
                    info['education'].append(education_text)
        
        # Extract certifications
        cert_keywords = ['certified', 'certification', 'certificate', 'aws', 'azure', 'google cloud', 'oracle']
        cert_patterns = [
            r'(?:certified|certification|certificate)\s+([^.\n]+)',
            r'(aws|azure|google cloud|oracle)\s+(?:certified\s+)?([^.\n]+)'
        ]
        
        for pattern in cert_patterns:
            matches = re.finditer(pattern, text.lower())
            for match in matches:
                cert_text = match.group(0).strip()
                if len(cert_text) > 5:
                    info['certifications'].append(cert_text)
        
        # Extract contact information
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        phone_pattern = r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
        
        email_match = re.search(email_pattern, text)
        if email_match:
            info['contact_info']['email'] = email_match.group(0)
        
        phone_match = re.search(phone_pattern, text)
        if phone_match:
            info['contact_info']['phone'] = phone_match.group(0)
        
        return info
    
    def calculate_skill_scores(self, categorized_skills: Dict[str, List[Dict[str, Any]]]) -> Dict[str, float]:
        """
        Calculate overall scores for each skill category.
        
        Args:
            categorized_skills (Dict[str, List[Dict[str, Any]]]): Categorized skills
            
        Returns:
            Dict[str, float]: Category scores
        """
        category_scores = {}
        
        for category, skills in categorized_skills.items():
            if not skills:
                category_scores[category] = 0.0
                continue
            
            total_score = 0.0
            total_weight = 0.0
            
            for skill in skills:
                confidence = skill.get('confidence', 0.0)
                years = skill.get('years', 0) or 0
                
                # Weight by years of experience and confidence
                weight = confidence * (1 + years * 0.1)  # Each year adds 10% weight
                total_score += weight
                total_weight += 1.0
            
            category_scores[category] = total_score / total_weight if total_weight > 0 else 0.0
        
        return category_scores
    
    def generate_summary(self, categorized_skills: Dict[str, List[Dict[str, Any]]], 
                        additional_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a comprehensive summary of the resume analysis.
        
        Args:
            categorized_skills (Dict[str, List[Dict[str, Any]]]): Categorized skills
            additional_info (Dict[str, Any]): Additional extracted information
            
        Returns:
            Dict[str, Any]: Comprehensive summary
        """
        category_scores = self.calculate_skill_scores(categorized_skills)
        
        # Count skills by category
        skill_counts = {category: len(skills) for category, skills in categorized_skills.items()}
        
        # Find top skills (by confidence and years)
        all_skills = []
        for skills in categorized_skills.values():
            all_skills.extend(skills)
        
        # Sort by a combination of confidence and years
        top_skills = sorted(
            all_skills,
            key=lambda x: (x.get('confidence', 0) * (1 + (x.get('years', 0) or 0) * 0.1)),
            reverse=True
        )[:10]  # Top 10 skills
        
        summary = {
            'total_skills_found': sum(skill_counts.values()),
            'skills_by_category': skill_counts,
            'category_scores': category_scores,
            'top_skills': top_skills,
            'total_experience_years': additional_info.get('total_experience'),
            'education_count': len(additional_info.get('education', [])),
            'certifications_count': len(additional_info.get('certifications', [])),
            'has_contact_info': bool(additional_info.get('contact_info', {})),
            'strongest_category': max(category_scores.items(), key=lambda x: x[1])[0] if category_scores else None
        }
        
        return summary
