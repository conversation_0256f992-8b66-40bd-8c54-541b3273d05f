"""
Comprehensive analyzer for detailed resume analysis including role matching and personal information extraction.
"""
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
import numpy as np
from config import Config

logger = logging.getLogger(__name__)

class ComprehensiveAnalyzer:
    """Advanced analyzer for comprehensive resume analysis."""
    
    def __init__(self):
        self.config = Config()
    
    def extract_personal_information(self, text: str) -> Dict[str, Any]:
        """
        Extract comprehensive personal information from resume text.
        
        Args:
            text (str): Resume text
            
        Returns:
            Dict[str, Any]: Personal information
        """
        personal_info = {
            'name': None,
            'email': None,
            'phone': None,
            'location': None,
            'linkedin': None,
            'github': None,
            'portfolio': None,
            'summary': None,
            'objective': None
        }
        
        # Extract name (usually first line or after common headers)
        name_patterns = [
            r'^([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)',  # First line name
            r'(?:Name|Full Name):\s*([A-Za-z\s]+)',
            r'^([A-Z][A-Z\s]+)$'  # All caps name
        ]
        
        lines = text.split('\n')
        for i, line in enumerate(lines[:5]):  # Check first 5 lines
            line = line.strip()
            if len(line) > 3 and len(line) < 50:
                for pattern in name_patterns:
                    match = re.search(pattern, line)
                    if match and not any(keyword in line.lower() for keyword in ['email', 'phone', 'address', 'resume']):
                        personal_info['name'] = match.group(1).strip()
                        break
                if personal_info['name']:
                    break
        
        # Extract email
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        email_match = re.search(email_pattern, text)
        if email_match:
            personal_info['email'] = email_match.group(0)
        
        # Extract phone
        phone_patterns = [
            r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',
            r'(?:\+\d{1,3}[-.\s]?)?\d{3}[-.\s]?\d{3}[-.\s]?\d{4}',
            r'(?:Phone|Tel|Mobile):\s*([\d\s\-\+\(\)]+)'
        ]
        
        for pattern in phone_patterns:
            phone_match = re.search(pattern, text)
            if phone_match:
                personal_info['phone'] = phone_match.group(0).strip()
                break
        
        # Extract location
        location_patterns = [
            r'(?:Address|Location|City):\s*([^,\n]+(?:,\s*[^,\n]+)*)',
            r'([A-Za-z\s]+,\s*[A-Z]{2}(?:\s+\d{5})?)',  # City, State ZIP
            r'([A-Za-z\s]+,\s*[A-Za-z\s]+,\s*[A-Za-z\s]+)'  # City, State, Country
        ]
        
        for pattern in location_patterns:
            location_match = re.search(pattern, text)
            if location_match:
                location = location_match.group(1).strip()
                if len(location) > 3 and len(location) < 100:
                    personal_info['location'] = location
                    break
        
        # Extract LinkedIn
        linkedin_patterns = [
            r'linkedin\.com/in/([a-zA-Z0-9\-]+)',
            r'(?:LinkedIn|linkedin):\s*(https?://[^\s]+)',
            r'(https?://(?:www\.)?linkedin\.com/in/[^\s]+)'
        ]
        
        for pattern in linkedin_patterns:
            linkedin_match = re.search(pattern, text, re.IGNORECASE)
            if linkedin_match:
                personal_info['linkedin'] = linkedin_match.group(0)
                break
        
        # Extract GitHub
        github_patterns = [
            r'github\.com/([a-zA-Z0-9\-]+)',
            r'(?:GitHub|github):\s*(https?://[^\s]+)',
            r'(https?://(?:www\.)?github\.com/[^\s]+)'
        ]
        
        for pattern in github_patterns:
            github_match = re.search(pattern, text, re.IGNORECASE)
            if github_match:
                personal_info['github'] = github_match.group(0)
                break
        
        # Extract portfolio/website
        portfolio_patterns = [
            r'(?:Portfolio|Website|Personal Site):\s*(https?://[^\s]+)',
            r'(https?://(?:www\.)?[a-zA-Z0-9\-]+\.(?:com|net|org|io|dev)[^\s]*)'
        ]
        
        for pattern in portfolio_patterns:
            portfolio_match = re.search(pattern, text, re.IGNORECASE)
            if portfolio_match:
                url = portfolio_match.group(1) if len(portfolio_match.groups()) > 0 else portfolio_match.group(0)
                if 'linkedin' not in url.lower() and 'github' not in url.lower():
                    personal_info['portfolio'] = url
                    break
        
        # Extract summary/objective
        summary_patterns = [
            r'(?:SUMMARY|Summary|PROFESSIONAL SUMMARY|Professional Summary)[:\s]*\n([^A-Z\n]*(?:\n[^A-Z\n]*)*)',
            r'(?:OBJECTIVE|Objective|CAREER OBJECTIVE|Career Objective)[:\s]*\n([^A-Z\n]*(?:\n[^A-Z\n]*)*)'
        ]
        
        for pattern in summary_patterns:
            summary_match = re.search(pattern, text, re.MULTILINE)
            if summary_match:
                summary_text = summary_match.group(1).strip()
                if len(summary_text) > 20:
                    if 'summary' in pattern.lower():
                        personal_info['summary'] = summary_text[:500]  # Limit length
                    else:
                        personal_info['objective'] = summary_text[:500]
                    break
        
        return personal_info
    
    def analyze_role_fit(self, skills_data: Dict[str, Any], text: str) -> Dict[str, Any]:
        """
        Analyze which roles the candidate is best suited for.
        
        Args:
            skills_data (Dict[str, Any]): Extracted skills with experience
            text (str): Resume text for keyword analysis
            
        Returns:
            Dict[str, Any]: Role fit analysis
        """
        role_scores = {}
        role_analysis = {}
        
        # Get all candidate skills
        candidate_skills = set(skill.lower() for skill in skills_data.keys())
        text_lower = text.lower()
        
        for role, requirements in self.config.ROLE_SKILL_MAPPING.items():
            score = 0.0
            analysis = {
                'score': 0.0,
                'required_skills_match': [],
                'preferred_skills_match': [],
                'missing_required_skills': [],
                'missing_preferred_skills': [],
                'keyword_matches': [],
                'experience_level': 'entry',
                'fit_percentage': 0.0,
                'strengths': [],
                'recommendations': []
            }
            
            # Check required skills
            required_matches = []
            missing_required = []
            for skill in requirements['required_skills']:
                if skill.lower() in candidate_skills:
                    required_matches.append(skill)
                    # Add experience bonus
                    years = skills_data.get(skill, {}).get('years', 0) or 0
                    score += 10 + (years * 2)  # Base 10 points + 2 per year
                else:
                    missing_required.append(skill)
            
            # Check preferred skills
            preferred_matches = []
            missing_preferred = []
            for skill in requirements['preferred_skills']:
                if skill.lower() in candidate_skills:
                    preferred_matches.append(skill)
                    years = skills_data.get(skill, {}).get('years', 0) or 0
                    score += 5 + years  # Base 5 points + 1 per year
                else:
                    missing_preferred.append(skill)
            
            # Check keywords in text
            keyword_matches = []
            for keyword in requirements['keywords']:
                if keyword.lower() in text_lower:
                    keyword_matches.append(keyword)
                    score += 3  # 3 points per keyword
            
            # Calculate weighted score based on skill categories
            category_scores = {}
            for category, weight in requirements['skill_weights'].items():
                category_skills = self.config.SOFTWARE_SKILLS.get(category, [])
                category_matches = [s for s in candidate_skills if s in [cs.lower() for cs in category_skills]]
                category_score = len(category_matches) * weight * 10
                category_scores[category] = category_score
                score += category_score
            
            # Determine experience level
            total_years = sum(
                (skills_data.get(skill, {}).get('years', 0) or 0) 
                for skill in required_matches + preferred_matches
            )
            avg_years = total_years / max(len(required_matches + preferred_matches), 1)
            
            if avg_years >= 5:
                experience_level = 'senior'
            elif avg_years >= 2:
                experience_level = 'mid'
            else:
                experience_level = 'entry'
            
            # Calculate fit percentage
            total_required = len(requirements['required_skills'])
            total_preferred = len(requirements['preferred_skills'])
            required_percentage = (len(required_matches) / total_required) * 100 if total_required > 0 else 0
            preferred_percentage = (len(preferred_matches) / total_preferred) * 100 if total_preferred > 0 else 0
            fit_percentage = (required_percentage * 0.7) + (preferred_percentage * 0.3)
            
            # Generate strengths and recommendations
            strengths = []
            recommendations = []
            
            if len(required_matches) >= total_required * 0.8:
                strengths.append(f"Strong foundation in {role.replace('_', ' ')} core technologies")
            
            if len(keyword_matches) >= len(requirements['keywords']) * 0.6:
                strengths.append(f"Relevant {role.replace('_', ' ')} experience and terminology")
            
            if avg_years >= requirements['min_experience']:
                strengths.append(f"Meets experience requirements ({avg_years:.1f} years average)")
            
            if missing_required:
                recommendations.append(f"Develop skills in: {', '.join(missing_required[:3])}")
            
            if avg_years < requirements['min_experience']:
                recommendations.append(f"Gain more experience (current: {avg_years:.1f}, required: {requirements['min_experience']})")
            
            # Update analysis
            analysis.update({
                'score': score,
                'required_skills_match': required_matches,
                'preferred_skills_match': preferred_matches,
                'missing_required_skills': missing_required,
                'missing_preferred_skills': missing_preferred,
                'keyword_matches': keyword_matches,
                'experience_level': experience_level,
                'fit_percentage': fit_percentage,
                'strengths': strengths,
                'recommendations': recommendations,
                'category_scores': category_scores
            })
            
            role_scores[role] = score
            role_analysis[role] = analysis
        
        # Sort roles by score
        sorted_roles = sorted(role_scores.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'best_fit_role': sorted_roles[0][0] if sorted_roles else None,
            'role_rankings': sorted_roles,
            'detailed_analysis': role_analysis,
            'top_3_roles': sorted_roles[:3]
        }

    def analyze_skill_combinations(self, skills_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze skill combinations and technology stacks.

        Args:
            skills_data (Dict[str, Any]): Skills with experience data

        Returns:
            Dict[str, Any]: Skill combination analysis
        """
        skill_combinations = {
            'technology_stacks': [],
            'skill_clusters': {},
            'complementary_skills': {},
            'skill_gaps': {},
            'stack_completeness': {}
        }

        # Define common technology stacks
        tech_stacks = {
            'MEAN': ['mongodb', 'express', 'angular', 'node.js'],
            'MERN': ['mongodb', 'express', 'react', 'node.js'],
            'LAMP': ['linux', 'apache', 'mysql', 'php'],
            'Django Stack': ['python', 'django', 'postgresql', 'redis'],
            'Spring Stack': ['java', 'spring', 'mysql', 'maven'],
            'Data Science Stack': ['python', 'pandas', 'numpy', 'scikit-learn', 'jupyter'],
            'ML Engineering Stack': ['python', 'tensorflow', 'docker', 'kubernetes', 'aws'],
            'DevOps Stack': ['docker', 'kubernetes', 'jenkins', 'terraform', 'aws'],
            'React Native Stack': ['react native', 'javascript', 'redux', 'firebase'],
            'Flutter Stack': ['flutter', 'dart', 'firebase', 'android sdk']
        }

        candidate_skills = set(skill.lower() for skill in skills_data.keys())

        # Analyze technology stack completeness
        for stack_name, stack_skills in tech_stacks.items():
            matches = [skill for skill in stack_skills if skill.lower() in candidate_skills]
            completeness = len(matches) / len(stack_skills) * 100

            if completeness > 0:
                skill_combinations['technology_stacks'].append({
                    'stack': stack_name,
                    'completeness': completeness,
                    'matched_skills': matches,
                    'missing_skills': [skill for skill in stack_skills if skill.lower() not in candidate_skills],
                    'experience_level': self._calculate_stack_experience(matches, skills_data)
                })

        # Sort stacks by completeness
        skill_combinations['technology_stacks'].sort(key=lambda x: x['completeness'], reverse=True)

        # Analyze skill clusters by category
        for category, category_skills in self.config.SOFTWARE_SKILLS.items():
            matched_skills = []
            for skill in category_skills:
                if skill.lower() in candidate_skills:
                    skill_info = skills_data.get(skill, {})
                    matched_skills.append({
                        'skill': skill,
                        'years': skill_info.get('years', 0),
                        'confidence': skill_info.get('confidence', 0.0)
                    })

            if matched_skills:
                skill_combinations['skill_clusters'][category] = {
                    'skills': matched_skills,
                    'count': len(matched_skills),
                    'avg_experience': sum(s.get('years', 0) or 0 for s in matched_skills) / len(matched_skills),
                    'strength_level': self._categorize_strength_level(matched_skills)
                }

        return skill_combinations

    def _calculate_stack_experience(self, matched_skills: List[str], skills_data: Dict[str, Any]) -> str:
        """Calculate experience level for a technology stack."""
        if not matched_skills:
            return 'none'

        total_years = sum(skills_data.get(skill, {}).get('years', 0) or 0 for skill in matched_skills)
        avg_years = total_years / len(matched_skills)

        if avg_years >= 4:
            return 'expert'
        elif avg_years >= 2:
            return 'intermediate'
        elif avg_years >= 1:
            return 'beginner'
        else:
            return 'novice'

    def _categorize_strength_level(self, skills: List[Dict[str, Any]]) -> str:
        """Categorize strength level based on skill count and experience."""
        if not skills:
            return 'none'

        count = len(skills)
        avg_years = sum(s.get('years', 0) or 0 for s in skills) / count
        avg_confidence = sum(s.get('confidence', 0.0) for s in skills) / count

        score = (count * 0.3) + (avg_years * 0.4) + (avg_confidence * 0.3)

        if score >= 8:
            return 'expert'
        elif score >= 5:
            return 'strong'
        elif score >= 3:
            return 'moderate'
        else:
            return 'basic'

    def generate_detailed_report(self, personal_info: Dict[str, Any],
                               role_analysis: Dict[str, Any],
                               skill_combinations: Dict[str, Any],
                               skills_data: Dict[str, Any],
                               additional_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a comprehensive detailed report.

        Args:
            personal_info (Dict[str, Any]): Personal information
            role_analysis (Dict[str, Any]): Role fit analysis
            skill_combinations (Dict[str, Any]): Skill combination analysis
            skills_data (Dict[str, Any]): Skills with experience
            additional_info (Dict[str, Any]): Additional extracted info

        Returns:
            Dict[str, Any]: Comprehensive report
        """
        report = {
            'candidate_profile': {
                'personal_info': personal_info,
                'professional_summary': self._generate_professional_summary(personal_info, skills_data, role_analysis),
                'contact_completeness': self._assess_contact_completeness(personal_info)
            },
            'role_recommendations': {
                'best_fit': role_analysis.get('best_fit_role'),
                'top_matches': role_analysis.get('top_3_roles', []),
                'detailed_fit_analysis': role_analysis.get('detailed_analysis', {}),
                'career_progression': self._suggest_career_progression(role_analysis, skills_data)
            },
            'technical_assessment': {
                'skill_summary': self._generate_skill_summary(skills_data),
                'technology_stacks': skill_combinations.get('technology_stacks', []),
                'skill_clusters': skill_combinations.get('skill_clusters', {}),
                'technical_strengths': self._identify_technical_strengths(skills_data, skill_combinations),
                'skill_gaps': self._identify_skill_gaps(role_analysis, skills_data)
            },
            'experience_analysis': {
                'total_experience': additional_info.get('total_experience'),
                'experience_distribution': self._analyze_experience_distribution(skills_data),
                'seniority_indicators': self._assess_seniority_indicators(skills_data, additional_info),
                'learning_trajectory': self._assess_learning_trajectory(skills_data)
            },
            'recommendations': {
                'immediate_actions': self._generate_immediate_recommendations(role_analysis, skills_data),
                'skill_development': self._generate_skill_development_plan(role_analysis, skills_data),
                'career_advice': self._generate_career_advice(role_analysis, skills_data, additional_info)
            },
            'overall_assessment': {
                'marketability_score': self._calculate_marketability_score(skills_data, role_analysis, additional_info),
                'unique_value_proposition': self._identify_unique_value_proposition(skills_data, skill_combinations),
                'competitive_advantages': self._identify_competitive_advantages(skills_data, role_analysis)
            }
        }

        return report

    def _generate_professional_summary(self, personal_info: Dict[str, Any],
                                     skills_data: Dict[str, Any],
                                     role_analysis: Dict[str, Any]) -> str:
        """Generate a professional summary based on analysis."""
        name = personal_info.get('name', 'Candidate')
        best_role = role_analysis.get('best_fit_role', 'software developer').replace('_', ' ').title()

        # Count skills by category
        skill_counts = {}
        for category, category_skills in self.config.SOFTWARE_SKILLS.items():
            count = sum(1 for skill in skills_data.keys() if skill.lower() in [cs.lower() for cs in category_skills])
            if count > 0:
                skill_counts[category] = count

        # Get top skills
        top_skills = sorted(skills_data.items(),
                          key=lambda x: (x[1].get('confidence', 0) * (1 + (x[1].get('years', 0) or 0))),
                          reverse=True)[:5]

        summary = f"{name} appears to be best suited for a {best_role} role. "

        if skill_counts:
            top_category = max(skill_counts.items(), key=lambda x: x[1])
            summary += f"Strong background in {top_category[0].replace('_', ' ')} with {top_category[1]} identified skills. "

        if top_skills:
            top_skill_names = [skill[0] for skill in top_skills[:3]]
            summary += f"Key expertise includes {', '.join(top_skill_names)}. "

        return summary

    def _assess_contact_completeness(self, personal_info: Dict[str, Any]) -> Dict[str, Any]:
        """Assess completeness of contact information."""
        required_fields = ['name', 'email', 'phone']
        optional_fields = ['location', 'linkedin', 'github', 'portfolio']

        completeness = {
            'required_complete': sum(1 for field in required_fields if personal_info.get(field)),
            'required_total': len(required_fields),
            'optional_complete': sum(1 for field in optional_fields if personal_info.get(field)),
            'optional_total': len(optional_fields),
            'missing_required': [field for field in required_fields if not personal_info.get(field)],
            'missing_optional': [field for field in optional_fields if not personal_info.get(field)]
        }

        completeness['required_percentage'] = (completeness['required_complete'] / completeness['required_total']) * 100
        completeness['optional_percentage'] = (completeness['optional_complete'] / completeness['optional_total']) * 100
        completeness['overall_score'] = (completeness['required_percentage'] * 0.7) + (completeness['optional_percentage'] * 0.3)

        return completeness

    def _generate_skill_summary(self, skills_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a comprehensive skill summary."""
        total_skills = len(skills_data)
        skills_with_experience = sum(1 for skill_info in skills_data.values() if skill_info.get('years'))

        # Calculate average experience
        total_years = sum(skill_info.get('years', 0) or 0 for skill_info in skills_data.values())
        avg_experience = total_years / max(skills_with_experience, 1)

        # Categorize by experience level
        experience_levels = {'expert': 0, 'advanced': 0, 'intermediate': 0, 'beginner': 0, 'novice': 0}

        for skill_info in skills_data.values():
            years = skill_info.get('years', 0) or 0
            if years >= 5:
                experience_levels['expert'] += 1
            elif years >= 3:
                experience_levels['advanced'] += 1
            elif years >= 2:
                experience_levels['intermediate'] += 1
            elif years >= 1:
                experience_levels['beginner'] += 1
            else:
                experience_levels['novice'] += 1

        return {
            'total_skills': total_skills,
            'skills_with_experience': skills_with_experience,
            'average_experience': avg_experience,
            'experience_distribution': experience_levels,
            'most_experienced_skill': max(skills_data.items(), key=lambda x: x[1].get('years', 0) or 0)[0] if skills_data else None
        }

    def _identify_technical_strengths(self, skills_data: Dict[str, Any], skill_combinations: Dict[str, Any]) -> List[str]:
        """Identify key technical strengths."""
        strengths = []

        # Check for complete technology stacks
        complete_stacks = [stack for stack in skill_combinations.get('technology_stacks', []) if stack['completeness'] >= 75]
        if complete_stacks:
            strengths.append(f"Complete mastery of {complete_stacks[0]['stack']} technology stack")

        # Check for strong skill clusters
        skill_clusters = skill_combinations.get('skill_clusters', {})
        for category, cluster_info in skill_clusters.items():
            if cluster_info['strength_level'] in ['expert', 'strong'] and cluster_info['count'] >= 3:
                strengths.append(f"Strong {category.replace('_', ' ')} expertise with {cluster_info['count']} skills")

        # Check for high-experience skills
        expert_skills = [skill for skill, info in skills_data.items() if (info.get('years', 0) or 0) >= 5]
        if expert_skills:
            strengths.append(f"Expert-level experience in {', '.join(expert_skills[:3])}")

        return strengths[:5]  # Limit to top 5 strengths

    def _identify_skill_gaps(self, role_analysis: Dict[str, Any], skills_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """Identify skill gaps for top roles."""
        gaps = {}

        detailed_analysis = role_analysis.get('detailed_analysis', {})
        for role, analysis in list(detailed_analysis.items())[:3]:  # Top 3 roles
            missing_required = analysis.get('missing_required_skills', [])
            missing_preferred = analysis.get('missing_preferred_skills', [])

            gaps[role] = {
                'critical_gaps': missing_required,
                'enhancement_opportunities': missing_preferred[:5]  # Limit to top 5
            }

        return gaps

    def _analyze_experience_distribution(self, skills_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze how experience is distributed across skills."""
        years_list = [info.get('years', 0) or 0 for info in skills_data.values()]

        if not years_list:
            return {'distribution': 'no_experience_data'}

        years_array = np.array(years_list)

        return {
            'mean_years': float(np.mean(years_array)),
            'median_years': float(np.median(years_array)),
            'std_deviation': float(np.std(years_array)),
            'max_years': int(np.max(years_array)),
            'min_years': int(np.min(years_array)),
            'distribution_type': 'specialized' if np.std(years_array) > 2 else 'balanced'
        }

    def _assess_seniority_indicators(self, skills_data: Dict[str, Any], additional_info: Dict[str, Any]) -> Dict[str, Any]:
        """Assess indicators of seniority level."""
        indicators = {
            'leadership_keywords': 0,
            'architecture_keywords': 0,
            'mentoring_keywords': 0,
            'project_management_keywords': 0,
            'senior_level_skills': 0
        }

        # This would be enhanced with actual text analysis
        # For now, we'll use skill-based indicators

        senior_skills = ['kubernetes', 'terraform', 'microservices', 'system design', 'architecture']
        indicators['senior_level_skills'] = sum(1 for skill in senior_skills if skill in [s.lower() for s in skills_data.keys()])

        # Calculate overall seniority score
        total_experience = additional_info.get('total_experience', 0) or 0
        avg_skill_experience = sum(info.get('years', 0) or 0 for info in skills_data.values()) / max(len(skills_data), 1)

        seniority_score = (total_experience * 0.4) + (avg_skill_experience * 0.3) + (indicators['senior_level_skills'] * 0.3)

        if seniority_score >= 8:
            level = 'senior'
        elif seniority_score >= 4:
            level = 'mid'
        else:
            level = 'junior'

        indicators['overall_level'] = level
        indicators['seniority_score'] = seniority_score

        return indicators

    def _assess_learning_trajectory(self, skills_data: Dict[str, Any]) -> str:
        """Assess the candidate's learning trajectory."""
        if not skills_data:
            return 'insufficient_data'

        # Analyze skill diversity and depth
        skill_count = len(skills_data)
        avg_experience = sum(info.get('years', 0) or 0 for info in skills_data.values()) / skill_count

        if skill_count >= 15 and avg_experience >= 3:
            return 'polyglot_expert'
        elif skill_count >= 10 and avg_experience >= 2:
            return 'broad_specialist'
        elif skill_count <= 5 and avg_experience >= 4:
            return 'deep_specialist'
        elif skill_count >= 8:
            return 'continuous_learner'
        else:
            return 'focused_learner'

    def _generate_immediate_recommendations(self, role_analysis: Dict[str, Any], skills_data: Dict[str, Any]) -> List[str]:
        """Generate immediate actionable recommendations."""
        recommendations = []

        best_role_analysis = role_analysis.get('detailed_analysis', {}).get(role_analysis.get('best_fit_role', ''), {})

        # Missing critical skills
        missing_required = best_role_analysis.get('missing_required_skills', [])
        if missing_required:
            recommendations.append(f"Priority: Learn {missing_required[0]} to meet core requirements for {role_analysis.get('best_fit_role', '').replace('_', ' ')}")

        # Experience gaps
        fit_percentage = best_role_analysis.get('fit_percentage', 0)
        if fit_percentage < 70:
            recommendations.append("Focus on building practical experience through projects or contributions")

        # Portfolio improvements
        if not any('github' in str(v).lower() for v in skills_data.values()):
            recommendations.append("Create a GitHub portfolio showcasing your technical skills")

        return recommendations[:3]  # Top 3 immediate actions

    def _generate_skill_development_plan(self, role_analysis: Dict[str, Any], skills_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """Generate a structured skill development plan."""
        plan = {
            'short_term': [],  # 1-3 months
            'medium_term': [], # 3-6 months
            'long_term': []    # 6+ months
        }

        best_role = role_analysis.get('best_fit_role', '')
        best_role_analysis = role_analysis.get('detailed_analysis', {}).get(best_role, {})

        missing_required = best_role_analysis.get('missing_required_skills', [])
        missing_preferred = best_role_analysis.get('missing_preferred_skills', [])

        # Short-term: Critical missing skills
        plan['short_term'] = missing_required[:2]

        # Medium-term: Preferred skills and complementary technologies
        plan['medium_term'] = missing_preferred[:3]

        # Long-term: Advanced skills and emerging technologies
        advanced_skills = ['kubernetes', 'microservices', 'system design', 'machine learning', 'blockchain']
        current_skills = set(skill.lower() for skill in skills_data.keys())
        plan['long_term'] = [skill for skill in advanced_skills if skill not in current_skills][:3]

        return plan

    def _generate_career_advice(self, role_analysis: Dict[str, Any], skills_data: Dict[str, Any], additional_info: Dict[str, Any]) -> List[str]:
        """Generate career progression advice."""
        advice = []

        total_experience = additional_info.get('total_experience', 0) or 0
        best_role = role_analysis.get('best_fit_role', '').replace('_', ' ')

        if total_experience < 2:
            advice.append(f"Focus on gaining hands-on experience in {best_role} through internships, projects, or entry-level positions")
        elif total_experience < 5:
            advice.append(f"Consider specializing deeper in {best_role} or exploring related senior roles")
        else:
            advice.append(f"Ready for senior {best_role} roles or technical leadership positions")

        # Technology stack advice
        top_3_roles = role_analysis.get('top_3_roles', [])
        if len(top_3_roles) > 1:
            advice.append(f"Your skills align with multiple roles: consider {', '.join([role[0].replace('_', ' ') for role in top_3_roles[:2]])}")

        return advice

    def _calculate_marketability_score(self, skills_data: Dict[str, Any], role_analysis: Dict[str, Any], additional_info: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall marketability score."""
        score_components = {
            'skill_diversity': min(len(skills_data) / 10 * 100, 100),  # Max 100 for 10+ skills
            'experience_depth': min(sum(info.get('years', 0) or 0 for info in skills_data.values()) / 20 * 100, 100),
            'role_fit': role_analysis.get('detailed_analysis', {}).get(role_analysis.get('best_fit_role', ''), {}).get('fit_percentage', 0),
            'modern_skills': self._assess_modern_skills(skills_data)
        }

        # Weighted average
        weights = {'skill_diversity': 0.25, 'experience_depth': 0.30, 'role_fit': 0.30, 'modern_skills': 0.15}
        overall_score = sum(score_components[component] * weights[component] for component in score_components)

        return {
            'overall_score': overall_score,
            'components': score_components,
            'grade': self._score_to_grade(overall_score),
            'market_readiness': 'high' if overall_score >= 80 else 'medium' if overall_score >= 60 else 'developing'
        }

    def _assess_modern_skills(self, skills_data: Dict[str, Any]) -> float:
        """Assess presence of modern/trending technologies."""
        modern_skills = [
            'react', 'vue', 'angular', 'node.js', 'typescript', 'python', 'go', 'rust',
            'docker', 'kubernetes', 'aws', 'azure', 'gcp', 'terraform', 'jenkins',
            'mongodb', 'postgresql', 'redis', 'elasticsearch', 'graphql', 'rest api'
        ]

        candidate_skills = set(skill.lower() for skill in skills_data.keys())
        modern_skill_count = sum(1 for skill in modern_skills if skill in candidate_skills)

        return min(modern_skill_count / 10 * 100, 100)  # Max 100 for 10+ modern skills

    def _score_to_grade(self, score: float) -> str:
        """Convert numerical score to letter grade."""
        if score >= 90:
            return 'A+'
        elif score >= 85:
            return 'A'
        elif score >= 80:
            return 'A-'
        elif score >= 75:
            return 'B+'
        elif score >= 70:
            return 'B'
        elif score >= 65:
            return 'B-'
        elif score >= 60:
            return 'C+'
        elif score >= 55:
            return 'C'
        else:
            return 'C-'

    def _identify_unique_value_proposition(self, skills_data: Dict[str, Any], skill_combinations: Dict[str, Any]) -> str:
        """Identify the candidate's unique value proposition."""
        # Find the most complete technology stack
        tech_stacks = skill_combinations.get('technology_stacks', [])
        if tech_stacks and tech_stacks[0]['completeness'] >= 75:
            return f"Full-stack expertise in {tech_stacks[0]['stack']} with {tech_stacks[0]['completeness']:.0f}% stack completeness"

        # Find strongest skill cluster
        skill_clusters = skill_combinations.get('skill_clusters', {})
        strongest_cluster = max(skill_clusters.items(), key=lambda x: x[1]['count']) if skill_clusters else None

        if strongest_cluster and strongest_cluster[1]['count'] >= 5:
            return f"Deep expertise in {strongest_cluster[0].replace('_', ' ')} with {strongest_cluster[1]['count']} related skills"

        # Find highest experience skill
        if skills_data:
            top_skill = max(skills_data.items(), key=lambda x: x[1].get('years', 0) or 0)
            if top_skill[1].get('years', 0) >= 5:
                return f"Senior-level expertise in {top_skill[0]} with {top_skill[1]['years']} years of experience"

        return "Versatile technical professional with diverse skill set"

    def _identify_competitive_advantages(self, skills_data: Dict[str, Any], role_analysis: Dict[str, Any]) -> List[str]:
        """Identify competitive advantages."""
        advantages = []

        # High-demand skills
        high_demand_skills = ['kubernetes', 'aws', 'react', 'python', 'tensorflow', 'docker']
        candidate_skills = set(skill.lower() for skill in skills_data.keys())
        matching_high_demand = [skill for skill in high_demand_skills if skill in candidate_skills]

        if matching_high_demand:
            advantages.append(f"Proficiency in high-demand technologies: {', '.join(matching_high_demand[:3])}")

        # Multiple role fit
        top_roles = role_analysis.get('top_3_roles', [])
        if len(top_roles) >= 2 and top_roles[1][1] >= top_roles[0][1] * 0.8:  # Second role within 80% of top role
            advantages.append(f"Versatility to work in multiple roles: {top_roles[0][0].replace('_', ' ')} and {top_roles[1][0].replace('_', ' ')}")

        # Broad experience
        if len(skills_data) >= 12:
            advantages.append(f"Broad technical knowledge across {len(skills_data)} different technologies")

        return advantages[:3]
