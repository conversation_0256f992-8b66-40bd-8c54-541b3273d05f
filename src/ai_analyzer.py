"""
AI analyzer module for processing resume text using Hugging Face models.
"""
import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from transformers import AutoTokenizer, AutoModelForTokenClassification, pipeline
import torch
from config import Config

logger = logging.getLogger(__name__)

class AIAnalyzer:
    """Class for analyzing resume text using AI models."""
    
    def __init__(self, model_name: Optional[str] = None):
        """
        Initialize the AI analyzer with a specified model.
        
        Args:
            model_name (str, optional): Name of the Hugging Face model to use
        """
        self.config = Config()
        self.model_name = model_name or self.config.SKILLS_MODEL_NAME
        self.tokenizer = None
        self.model = None
        self.ner_pipeline = None
        self._load_model()
    
    def _load_model(self):
        """Load the tokenizer and model."""
        try:
            logger.info(f"Loading model: {self.model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForTokenClassification.from_pretrained(self.model_name)
            
            # Create NER pipeline
            self.ner_pipeline = pipeline(
                "ner",
                model=self.model,
                tokenizer=self.tokenizer,
                aggregation_strategy="simple",
                device=0 if torch.cuda.is_available() else -1
            )
            
            logger.info("Model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise
    
    def extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract named entities from text using the NER model.
        
        Args:
            text (str): Input text to analyze
            
        Returns:
            List[Dict[str, Any]]: List of extracted entities
        """
        try:
            # Split text into chunks if it's too long
            max_length = 512  # BERT's max sequence length
            chunks = self._split_text(text, max_length)
            
            all_entities = []
            for chunk in chunks:
                entities = self.ner_pipeline(chunk)
                # Filter entities by confidence score
                filtered_entities = [
                    entity for entity in entities 
                    if entity['score'] >= self.config.MIN_CONFIDENCE_SCORE
                ]
                all_entities.extend(filtered_entities)
            
            return all_entities
            
        except Exception as e:
            logger.error(f"Error extracting entities: {str(e)}")
            return []
    
    def _split_text(self, text: str, max_length: int) -> List[str]:
        """
        Split text into chunks that fit within model's max length.
        
        Args:
            text (str): Text to split
            max_length (int): Maximum length per chunk
            
        Returns:
            List[str]: List of text chunks
        """
        # Simple sentence-based splitting
        sentences = re.split(r'[.!?]+', text)
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            # Rough token estimation (1 token ≈ 4 characters)
            estimated_tokens = len(current_chunk + sentence) // 4
            
            if estimated_tokens > max_length and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk += " " + sentence if current_chunk else sentence
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def extract_skills_from_entities(self, entities: List[Dict[str, Any]], text: str) -> List[Dict[str, Any]]:
        """
        Extract skills from NER entities and match with predefined skills.
        
        Args:
            entities (List[Dict[str, Any]]): NER entities
            text (str): Original text for context
            
        Returns:
            List[Dict[str, Any]]: Extracted skills with metadata
        """
        skills = []
        predefined_skills = self.config.get_all_skills()
        text_lower = text.lower()
        
        # Extract from NER entities
        for entity in entities:
            entity_text = entity['word'].lower().replace('##', '')
            
            # Check if entity matches predefined skills
            for skill in predefined_skills:
                if skill.lower() in entity_text or entity_text in skill.lower():
                    skills.append({
                        'skill': skill,
                        'confidence': float(entity['score']),
                        'source': 'ner_entity',
                        'context': entity.get('word', '')
                    })
        
        # Enhanced pattern matching for predefined skills
        for skill in predefined_skills:
            # Multiple pattern variations for better matching
            patterns = [
                r'\b' + re.escape(skill.lower()) + r'\b',  # Exact match
                r'\b' + re.escape(skill.lower()) + r's?\b',  # Plural form
                r'\b' + re.escape(skill.lower().replace('.', r'\.')) + r'\b',  # Handle dots
                r'\b' + re.escape(skill.lower().replace(' ', r'[-\s]')) + r'\b',  # Handle spaces/hyphens
            ]

            # Special patterns for common variations
            if skill.lower() == 'javascript':
                patterns.append(r'\bjs\b')
            elif skill.lower() == 'typescript':
                patterns.append(r'\bts\b')
            elif skill.lower() == 'postgresql':
                patterns.extend([r'\bpostgres\b', r'\bpsql\b'])
            elif skill.lower() == 'mongodb':
                patterns.append(r'\bmongo\b')
            elif skill.lower() == 'kubernetes':
                patterns.append(r'\bk8s\b')
            elif skill.lower() == 'docker':
                patterns.append(r'\bcontainer\b')

            skill_found = False
            for pattern in patterns:
                matches = list(re.finditer(pattern, text_lower))

                if matches and not skill_found:
                    # Extract surrounding context from first match
                    match = matches[0]
                    start = max(0, match.start() - 50)
                    end = min(len(text), match.end() + 50)
                    context = text[start:end].strip()

                    # Higher confidence for exact matches, lower for variations
                    confidence = 1.0 if pattern == patterns[0] else 0.9

                    skills.append({
                        'skill': skill,
                        'confidence': confidence,
                        'source': 'pattern_match',
                        'context': context
                    })
                    skill_found = True
                    break
        
        # Remove duplicates and sort by confidence
        unique_skills = {}
        for skill_info in skills:
            skill_name = skill_info['skill'].lower()
            if skill_name not in unique_skills or skill_info['confidence'] > unique_skills[skill_name]['confidence']:
                unique_skills[skill_name] = skill_info
        
        return sorted(unique_skills.values(), key=lambda x: x['confidence'], reverse=True)
    
    def extract_experience_years(self, text: str, skills: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Extract years of experience for identified skills.
        
        Args:
            text (str): Resume text
            skills (List[Dict[str, Any]]): Identified skills
            
        Returns:
            Dict[str, Any]: Skills with experience years
        """
        experience_data = {}
        
        # Enhanced patterns to match experience mentions
        year_patterns = [
            # Standard patterns
            r'(\d+)\s*(?:years?|yrs?)\s*(?:of\s*)?(?:experience\s*)?(?:with\s*|in\s*|using\s*|of\s*)?([^.\n]+)',
            r'([^.\n]+)\s*(?:for\s*|:\s*|\(\s*)(\d+)\s*(?:years?|yrs?)',
            r'(\d+)\+?\s*(?:years?|yrs?)\s*(?:of\s*|with\s*|in\s*|using\s*)?([^.\n]+)',

            # Parenthetical patterns like "Python (5 years)"
            r'([^.\n\(]+)\s*\(\s*(\d+)\s*(?:years?|yrs?)\s*\)',

            # Bullet point patterns
            r'•\s*([^.\n]+)\s*(?::\s*)?(\d+)\s*(?:years?|yrs?)',
            r'-\s*([^.\n]+)\s*(?::\s*)?(\d+)\s*(?:years?|yrs?)',

            # Experience section patterns
            r'(?:experience|worked|used|utilizing|proficient)\s+(?:with\s+|in\s+)?([^.\n]+?)\s+(?:for\s+)?(\d+)\s*(?:years?|yrs?)',

            # Skills list patterns
            r'([a-zA-Z\s\+\#\.]+)\s*[-:]\s*(\d+)\s*(?:years?|yrs?)',
        ]
        
        text_lower = text.lower()
        
        for skill_info in skills:
            skill = skill_info['skill'].lower()
            years = None

            # Create skill variations for better matching
            skill_variations = [skill]
            if skill == 'javascript':
                skill_variations.extend(['js', 'java script'])
            elif skill == 'typescript':
                skill_variations.extend(['ts', 'type script'])
            elif skill == 'postgresql':
                skill_variations.extend(['postgres', 'psql'])
            elif skill == 'mongodb':
                skill_variations.extend(['mongo'])
            elif skill == 'kubernetes':
                skill_variations.extend(['k8s'])

            # Look for experience mentions near the skill
            for pattern in year_patterns:
                matches = re.finditer(pattern, text_lower, re.IGNORECASE | re.MULTILINE)

                for match in matches:
                    match_text = match.group(0).lower()
                    groups = match.groups()

                    # Check if any skill variation is mentioned in the match
                    skill_found = any(var in match_text for var in skill_variations)

                    if skill_found:
                        # Extract the number - it could be in different groups
                        year_candidates = []
                        for group in groups:
                            if group and re.search(r'\d+', group):
                                numbers = re.findall(r'\d+', group)
                                year_candidates.extend([int(n) for n in numbers if 0 < int(n) <= 50])

                        if year_candidates:
                            # Take the most reasonable year value
                            years = min(year_candidates)  # Avoid unrealistic high numbers
                            break

                if years:
                    break
            
            experience_data[skill_info['skill']] = {
                'years': years,
                'confidence': skill_info['confidence'],
                'context': skill_info.get('context', ''),
                'source': skill_info.get('source', '')
            }
        
        return experience_data
