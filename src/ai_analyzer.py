"""
AI analyzer module for processing resume text using Hugging Face models.
"""
import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from transformers import AutoTokenizer, AutoModelForTokenClassification, pipeline
import torch
from config import Config

logger = logging.getLogger(__name__)

class AIAnalyzer:
    """Class for analyzing resume text using AI models."""
    
    def __init__(self, model_name: Optional[str] = None):
        """
        Initialize the AI analyzer with a specified model.
        
        Args:
            model_name (str, optional): Name of the Hugging Face model to use
        """
        self.config = Config()
        self.model_name = model_name or self.config.SKILLS_MODEL_NAME
        self.tokenizer = None
        self.model = None
        self.ner_pipeline = None
        self._load_model()
    
    def _load_model(self):
        """Load the tokenizer and model."""
        try:
            logger.info(f"Loading model: {self.model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForTokenClassification.from_pretrained(self.model_name)
            
            # Create NER pipeline
            self.ner_pipeline = pipeline(
                "ner",
                model=self.model,
                tokenizer=self.tokenizer,
                aggregation_strategy="simple",
                device=0 if torch.cuda.is_available() else -1
            )
            
            logger.info("Model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise
    
    def extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract named entities from text using the NER model.
        
        Args:
            text (str): Input text to analyze
            
        Returns:
            List[Dict[str, Any]]: List of extracted entities
        """
        try:
            # Split text into chunks if it's too long
            max_length = 512  # BERT's max sequence length
            chunks = self._split_text(text, max_length)
            
            all_entities = []
            for chunk in chunks:
                entities = self.ner_pipeline(chunk)
                # Filter entities by confidence score
                filtered_entities = [
                    entity for entity in entities 
                    if entity['score'] >= self.config.MIN_CONFIDENCE_SCORE
                ]
                all_entities.extend(filtered_entities)
            
            return all_entities
            
        except Exception as e:
            logger.error(f"Error extracting entities: {str(e)}")
            return []
    
    def _split_text(self, text: str, max_length: int) -> List[str]:
        """
        Split text into chunks that fit within model's max length.
        
        Args:
            text (str): Text to split
            max_length (int): Maximum length per chunk
            
        Returns:
            List[str]: List of text chunks
        """
        # Simple sentence-based splitting
        sentences = re.split(r'[.!?]+', text)
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            # Rough token estimation (1 token ≈ 4 characters)
            estimated_tokens = len(current_chunk + sentence) // 4
            
            if estimated_tokens > max_length and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk += " " + sentence if current_chunk else sentence
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def extract_skills_from_entities(self, entities: List[Dict[str, Any]], text: str) -> List[Dict[str, Any]]:
        """
        Extract skills from NER entities and match with predefined skills.
        
        Args:
            entities (List[Dict[str, Any]]): NER entities
            text (str): Original text for context
            
        Returns:
            List[Dict[str, Any]]: Extracted skills with metadata
        """
        skills = []
        predefined_skills = self.config.get_all_skills()
        text_lower = text.lower()
        
        # Extract from NER entities
        for entity in entities:
            entity_text = entity['word'].lower().replace('##', '')
            
            # Check if entity matches predefined skills
            for skill in predefined_skills:
                if skill.lower() in entity_text or entity_text in skill.lower():
                    skills.append({
                        'skill': skill,
                        'confidence': float(entity['score']),
                        'source': 'ner_entity',
                        'context': entity.get('word', '')
                    })
        
        # Direct pattern matching for predefined skills
        for skill in predefined_skills:
            pattern = r'\b' + re.escape(skill.lower()) + r'\b'
            matches = re.finditer(pattern, text_lower)
            
            for match in matches:
                # Extract surrounding context
                start = max(0, match.start() - 50)
                end = min(len(text), match.end() + 50)
                context = text[start:end].strip()
                
                skills.append({
                    'skill': skill,
                    'confidence': 1.0,  # High confidence for exact matches
                    'source': 'pattern_match',
                    'context': context
                })
        
        # Remove duplicates and sort by confidence
        unique_skills = {}
        for skill_info in skills:
            skill_name = skill_info['skill'].lower()
            if skill_name not in unique_skills or skill_info['confidence'] > unique_skills[skill_name]['confidence']:
                unique_skills[skill_name] = skill_info
        
        return sorted(unique_skills.values(), key=lambda x: x['confidence'], reverse=True)
    
    def extract_experience_years(self, text: str, skills: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Extract years of experience for identified skills.
        
        Args:
            text (str): Resume text
            skills (List[Dict[str, Any]]): Identified skills
            
        Returns:
            Dict[str, Any]: Skills with experience years
        """
        experience_data = {}
        
        # Patterns to match experience mentions
        year_patterns = [
            r'(\d+)\s*(?:years?|yrs?)\s*(?:of\s*)?(?:experience\s*)?(?:with\s*|in\s*|using\s*)?([^.]+)',
            r'([^.]+)\s*(?:for\s*|:\s*)(\d+)\s*(?:years?|yrs?)',
            r'(\d+)\+?\s*(?:years?|yrs?)\s*([^.]+)',
        ]
        
        text_lower = text.lower()
        
        for skill_info in skills:
            skill = skill_info['skill'].lower()
            years = None
            
            # Look for experience mentions near the skill
            for pattern in year_patterns:
                matches = re.finditer(pattern, text_lower, re.IGNORECASE)
                
                for match in matches:
                    match_text = match.group(0).lower()
                    
                    # Check if skill is mentioned in the match
                    if skill in match_text:
                        # Extract the number
                        numbers = re.findall(r'\d+', match_text)
                        if numbers:
                            years = int(numbers[0])
                            break
                
                if years:
                    break
            
            experience_data[skill_info['skill']] = {
                'years': years,
                'confidence': skill_info['confidence'],
                'context': skill_info.get('context', ''),
                'source': skill_info.get('source', '')
            }
        
        return experience_data
