"""
PDF reader module for extracting text from resume PDFs.
"""
import os
import logging
from typing import Optional, Dict, Any
import PyPDF2
import pdfplumber
from config import Config

logger = logging.getLogger(__name__)

class PDFReader:
    """Class for reading and extracting text from PDF files."""
    
    def __init__(self):
        self.config = Config()
    
    def validate_file(self, file_path: str) -> bool:
        """
        Validate if the file exists and meets requirements.
        
        Args:
            file_path (str): Path to the PDF file
            
        Returns:
            bool: True if file is valid, False otherwise
        """
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return False
        
        # Check file extension
        _, ext = os.path.splitext(file_path)
        if ext.lower() not in self.config.SUPPORTED_FORMATS:
            logger.error(f"Unsupported file format: {ext}")
            return False
        
        # Check file size
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        if file_size_mb > self.config.MAX_FILE_SIZE_MB:
            logger.error(f"File too large: {file_size_mb:.2f}MB > {self.config.MAX_FILE_SIZE_MB}MB")
            return False
        
        return True
    
    def extract_text_pypdf2(self, file_path: str) -> Optional[str]:
        """
        Extract text using PyPDF2 library.
        
        Args:
            file_path (str): Path to the PDF file
            
        Returns:
            Optional[str]: Extracted text or None if failed
        """
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text() + "\n"
                
                return text.strip()
        
        except Exception as e:
            logger.error(f"Error extracting text with PyPDF2: {str(e)}")
            return None
    
    def extract_text_pdfplumber(self, file_path: str) -> Optional[str]:
        """
        Extract text using pdfplumber library (more accurate for complex layouts).
        
        Args:
            file_path (str): Path to the PDF file
            
        Returns:
            Optional[str]: Extracted text or None if failed
        """
        try:
            with pdfplumber.open(file_path) as pdf:
                text = ""
                
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
                
                return text.strip()
        
        except Exception as e:
            logger.error(f"Error extracting text with pdfplumber: {str(e)}")
            return None
    
    def extract_text(self, file_path: str, method: str = "pdfplumber") -> Dict[str, Any]:
        """
        Extract text from PDF file with fallback methods.
        
        Args:
            file_path (str): Path to the PDF file
            method (str): Preferred extraction method ("pdfplumber" or "pypdf2")
            
        Returns:
            Dict[str, Any]: Result containing text and metadata
        """
        result = {
            "success": False,
            "text": "",
            "method_used": None,
            "file_path": file_path,
            "error": None
        }
        
        # Validate file first
        if not self.validate_file(file_path):
            result["error"] = "File validation failed"
            return result
        
        # Try preferred method first
        if method == "pdfplumber":
            text = self.extract_text_pdfplumber(file_path)
            if text:
                result.update({
                    "success": True,
                    "text": text,
                    "method_used": "pdfplumber"
                })
                return result
            
            # Fallback to PyPDF2
            logger.warning("pdfplumber failed, trying PyPDF2...")
            text = self.extract_text_pypdf2(file_path)
            if text:
                result.update({
                    "success": True,
                    "text": text,
                    "method_used": "pypdf2"
                })
                return result
        
        else:  # method == "pypdf2"
            text = self.extract_text_pypdf2(file_path)
            if text:
                result.update({
                    "success": True,
                    "text": text,
                    "method_used": "pypdf2"
                })
                return result
            
            # Fallback to pdfplumber
            logger.warning("PyPDF2 failed, trying pdfplumber...")
            text = self.extract_text_pdfplumber(file_path)
            if text:
                result.update({
                    "success": True,
                    "text": text,
                    "method_used": "pdfplumber"
                })
                return result
        
        result["error"] = "All extraction methods failed"
        logger.error(f"Failed to extract text from {file_path}")
        return result
