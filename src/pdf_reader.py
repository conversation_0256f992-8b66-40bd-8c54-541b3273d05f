"""
PDF reader module for extracting text from resume PDFs.
"""
import os
import logging
import re
from typing import Optional, Dict, Any
import PyPDF2
import pdfplumber
from config import Config

# Try to import advanced parser, but don't fail if not available
try:
    from .advanced_pdf_parser import AdvancedPDFParser
    ADVANCED_PARSER_AVAILABLE = True
except ImportError as e:
    ADVANCED_PARSER_AVAILABLE = False
    logging.warning(f"Advanced PDF parser not available: {e}")
    logging.warning("Using basic PDF extraction only. Install pymupdf for enhanced features.")

logger = logging.getLogger(__name__)

class PDFReader:
    """Class for reading and extracting text from PDF files."""
    
    def __init__(self):
        self.config = Config()

        # Initialize advanced parser if available
        if ADVANCED_PARSER_AVAILABLE:
            try:
                self.advanced_parser = AdvancedPDFParser()
            except Exception as e:
                logging.warning(f"Failed to initialize advanced parser: {e}")
                self.advanced_parser = None
        else:
            self.advanced_parser = None
    
    def validate_file(self, file_path: str) -> bool:
        """
        Validate if the file exists and meets requirements.
        
        Args:
            file_path (str): Path to the PDF file
            
        Returns:
            bool: True if file is valid, False otherwise
        """
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return False
        
        # Check file extension
        _, ext = os.path.splitext(file_path)
        if ext.lower() not in self.config.SUPPORTED_FORMATS:
            logger.error(f"Unsupported file format: {ext}")
            return False
        
        # Check file size
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        if file_size_mb > self.config.MAX_FILE_SIZE_MB:
            logger.error(f"File too large: {file_size_mb:.2f}MB > {self.config.MAX_FILE_SIZE_MB}MB")
            return False
        
        return True
    
    def extract_text_pypdf2(self, file_path: str) -> Optional[str]:
        """
        Extract text using PyPDF2 library with enhanced text processing.

        Args:
            file_path (str): Path to the PDF file

        Returns:
            Optional[str]: Extracted text or None if failed
        """
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""

                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()

                    # Clean up the extracted text
                    if page_text:
                        # Fix common PDF extraction issues
                        page_text = self._clean_extracted_text(page_text)
                        text += page_text + "\n"

                return text.strip()

        except Exception as e:
            logger.error(f"Error extracting text with PyPDF2: {str(e)}")
            return None
    
    def extract_text_pdfplumber(self, file_path: str) -> Optional[str]:
        """
        Extract text using pdfplumber library with enhanced processing.

        Args:
            file_path (str): Path to the PDF file

        Returns:
            Optional[str]: Extracted text or None if failed
        """
        try:
            with pdfplumber.open(file_path) as pdf:
                text = ""

                for page in pdf.pages:
                    # Try multiple extraction methods
                    page_text = page.extract_text()

                    # If standard extraction fails, try with different settings
                    if not page_text or len(page_text.strip()) < 10:
                        page_text = page.extract_text(
                            x_tolerance=3,
                            y_tolerance=3,
                            layout=True,
                            x_density=7.25,
                            y_density=13
                        )

                    # Also try extracting tables if present
                    tables = page.extract_tables()
                    if tables:
                        for table in tables:
                            for row in table:
                                if row:
                                    table_text = " ".join([cell for cell in row if cell])
                                    page_text += "\n" + table_text

                    if page_text:
                        # Clean up the extracted text
                        page_text = self._clean_extracted_text(page_text)
                        text += page_text + "\n"

                return text.strip()

        except Exception as e:
            logger.error(f"Error extracting text with pdfplumber: {str(e)}")
            return None
    
    def extract_text(self, file_path: str, method: str = "advanced") -> Dict[str, Any]:
        """
        Extract text from PDF file with advanced methods and fallbacks.

        Args:
            file_path (str): Path to the PDF file
            method (str): Extraction method ("advanced", "pdfplumber", or "pypdf2")

        Returns:
            Dict[str, Any]: Result containing text and metadata
        """
        # Validate file first
        if not self.validate_file(file_path):
            return {
                "success": False,
                "text": "",
                "method_used": None,
                "file_path": file_path,
                "error": "File validation failed"
            }

        # Use advanced parser if available
        if method == "advanced" and self.advanced_parser:
            logger.info("Using advanced PDF parser with multiple extraction methods")
            try:
                result = self.advanced_parser.extract_text_comprehensive(file_path)
                result["file_path"] = file_path

                # If advanced parser succeeds, also extract structured data
                if result["success"]:
                    structured_data = self.advanced_parser.extract_structured_data(result["text"])
                    result["structured_data"] = structured_data
                    logger.info(f"Advanced parser succeeded with method: {result['method_used']}, score: {result['confidence_score']:.2f}")

                    # Log what was found
                    if structured_data.get('emails'):
                        logger.info(f"Found emails: {structured_data['emails']}")
                    if structured_data.get('phones'):
                        logger.info(f"Found phones: {structured_data['phones']}")

                return result
            except Exception as e:
                logger.warning(f"Advanced parser failed: {e}, falling back to basic methods")

        elif method == "advanced":
            logger.warning("Advanced parser not available, falling back to basic methods")

        # Fallback to legacy methods
        result = {
            "success": False,
            "text": "",
            "method_used": None,
            "file_path": file_path,
            "error": None
        }

        # Try preferred legacy method first
        if method == "pdfplumber":
            text = self.extract_text_pdfplumber(file_path)
            if text:
                result.update({
                    "success": True,
                    "text": text,
                    "method_used": "pdfplumber"
                })
                return result

            # Fallback to PyPDF2
            logger.warning("pdfplumber failed, trying PyPDF2...")
            text = self.extract_text_pypdf2(file_path)
            if text:
                result.update({
                    "success": True,
                    "text": text,
                    "method_used": "pypdf2"
                })
                return result

        else:  # method == "pypdf2"
            text = self.extract_text_pypdf2(file_path)
            if text:
                result.update({
                    "success": True,
                    "text": text,
                    "method_used": "pypdf2"
                })
                return result

            # Fallback to pdfplumber
            logger.warning("PyPDF2 failed, trying pdfplumber...")
            text = self.extract_text_pdfplumber(file_path)
            if text:
                result.update({
                    "success": True,
                    "text": text,
                    "method_used": "pdfplumber"
                })
                return result

        result["error"] = "All extraction methods failed"
        logger.error(f"Failed to extract text from {file_path}")
        return result

    def _clean_extracted_text(self, text: str) -> str:
        """
        Clean and normalize extracted text from PDF.

        Args:
            text (str): Raw extracted text

        Returns:
            str: Cleaned text
        """
        if not text:
            return ""

        # Fix common PDF extraction issues
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Fix broken words (common in PDF extraction)
        text = re.sub(r'([a-z])([A-Z])', r'\1 \2', text)

        # Fix email addresses that might be broken
        text = re.sub(r'([a-zA-Z0-9._%+-]+)\s*@\s*([a-zA-Z0-9.-]+)\s*\.\s*([a-zA-Z]{2,})', r'\1@\2.\3', text)

        # Fix phone numbers that might be broken
        text = re.sub(r'(\d{3})\s*[-.]?\s*(\d{3})\s*[-.]?\s*(\d{4})', r'\1-\2-\3', text)

        # Fix URLs that might be broken
        text = re.sub(r'(https?://)\s*([^\s]+)', r'\1\2', text)
        text = re.sub(r'(www\.)\s*([^\s]+)', r'\1\2', text)

        # Fix common LinkedIn/GitHub patterns
        text = re.sub(r'linkedin\s*\.\s*com\s*/\s*in\s*/\s*([^\s]+)', r'linkedin.com/in/\1', text, flags=re.IGNORECASE)
        text = re.sub(r'github\s*\.\s*com\s*/\s*([^\s]+)', r'github.com/\1', text, flags=re.IGNORECASE)

        # Remove extra spaces around punctuation
        text = re.sub(r'\s*([,.;:!?])\s*', r'\1 ', text)

        # Ensure proper line breaks
        text = re.sub(r'([.!?])\s*([A-Z])', r'\1\n\2', text)

        # Clean up multiple consecutive newlines
        text = re.sub(r'\n\s*\n', '\n\n', text)

        # Remove leading/trailing whitespace from each line
        lines = [line.strip() for line in text.split('\n')]
        text = '\n'.join(line for line in lines if line)

        return text.strip()
