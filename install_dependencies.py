#!/usr/bin/env python3
"""
Installation script to help install dependencies for the resume screening application.
"""
import subprocess
import sys
import importlib

def check_package(package_name, import_name=None):
    """Check if a package is installed."""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Main installation function."""
    print("🔧 Resume Screening Application - Dependency Installer")
    print("=" * 60)
    
    # Core dependencies (required)
    core_deps = [
        ("PyPDF2", "PyPDF2"),
        ("pdfplumber", "pdfplumber"),
        ("transformers", "transformers"),
        ("torch", "torch"),
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("python-dotenv", "dotenv"),
        ("spacy", "spacy"),
        ("regex", "regex"),
    ]
    
    # Optional dependencies (for enhanced features)
    optional_deps = [
        ("pymupdf", "fitz", "Enhanced PDF parsing"),
        ("sentence-transformers", "sentence_transformers", "Better AI models"),
        ("pytesseract", "pytesseract", "OCR support"),
        ("pdf2image", "pdf2image", "OCR support"),
        ("Pillow", "PIL", "Image processing"),
        ("pdfminer.six==20221105", "pdfminer", "Alternative PDF parser"),
    ]
    
    print("📦 Checking core dependencies...")
    missing_core = []
    
    for package, import_name in core_deps:
        if check_package(package, import_name):
            print(f"  ✅ {package}")
        else:
            print(f"  ❌ {package} - MISSING")
            missing_core.append(package)
    
    print(f"\n📊 Core dependencies: {len(core_deps) - len(missing_core)}/{len(core_deps)} installed")
    
    if missing_core:
        print(f"\n🔧 Installing missing core dependencies...")
        for package in missing_core:
            print(f"  Installing {package}...")
            if install_package(package):
                print(f"    ✅ {package} installed successfully")
            else:
                print(f"    ❌ Failed to install {package}")
    
    print(f"\n📦 Checking optional dependencies...")
    missing_optional = []
    
    for package_info in optional_deps:
        if len(package_info) == 3:
            package, import_name, description = package_info
        else:
            package, import_name = package_info
            description = "Enhanced features"
        
        if check_package(package, import_name):
            print(f"  ✅ {package} - {description}")
        else:
            print(f"  ⚠️  {package} - {description} (optional)")
            missing_optional.append((package, description))
    
    if missing_optional:
        print(f"\n🎯 Optional dependencies available for installation:")
        for i, (package, description) in enumerate(missing_optional, 1):
            print(f"  {i}. {package} - {description}")
        
        print(f"\nInstall optional dependencies? (y/n/selective): ", end="")
        choice = input().lower().strip()
        
        if choice == 'y':
            print(f"\n🔧 Installing all optional dependencies...")
            for package, description in missing_optional:
                print(f"  Installing {package}...")
                if install_package(package):
                    print(f"    ✅ {package} installed successfully")
                else:
                    print(f"    ⚠️  Failed to install {package} (optional)")
        
        elif choice == 'selective':
            print(f"\nSelect packages to install (comma-separated numbers): ", end="")
            selections = input().strip()
            try:
                indices = [int(x.strip()) - 1 for x in selections.split(',')]
                for i in indices:
                    if 0 <= i < len(missing_optional):
                        package, description = missing_optional[i]
                        print(f"  Installing {package}...")
                        if install_package(package):
                            print(f"    ✅ {package} installed successfully")
                        else:
                            print(f"    ⚠️  Failed to install {package}")
            except ValueError:
                print("  Invalid selection format")
    
    print(f"\n🧪 Testing installation...")
    
    # Test core functionality
    try:
        from src.pdf_reader import PDFReader
        print("  ✅ PDF reading functionality works")
    except ImportError as e:
        print(f"  ❌ PDF reading failed: {e}")
    
    try:
        from src.ai_analyzer import AIAnalyzer
        print("  ✅ AI analysis functionality works")
    except ImportError as e:
        print(f"  ❌ AI analysis failed: {e}")
    
    try:
        from src.comprehensive_analyzer import ComprehensiveAnalyzer
        print("  ✅ Comprehensive analysis functionality works")
    except ImportError as e:
        print(f"  ❌ Comprehensive analysis failed: {e}")
    
    # Test advanced features
    try:
        from src.advanced_pdf_parser import AdvancedPDFParser
        print("  ✅ Advanced PDF parser available")
    except ImportError:
        print("  ⚠️  Advanced PDF parser not available (install pymupdf for enhanced features)")
    
    print(f"\n🎉 Installation complete!")
    print(f"\n📋 Next steps:")
    print(f"  1. Test with a resume: python main.py 'your_resume.pdf'")
    print(f"  2. Run comprehensive test: python test_comprehensive_skills.py")
    print(f"  3. Check skill categorization: python test_skill_categorization.py")
    
    if missing_optional:
        print(f"\n💡 For enhanced features, consider installing:")
        for package, description in missing_optional[:3]:
            print(f"  pip install {package}  # {description}")

if __name__ == "__main__":
    main()
