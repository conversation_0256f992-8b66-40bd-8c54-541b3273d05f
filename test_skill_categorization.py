#!/usr/bin/env python3
"""
Test script to demonstrate enhanced skill categorization.
"""
from src.skills_extractor import SkillsExtractor
from config import Config

def test_skill_categorization():
    """Test the skill categorization functionality."""
    print("🛠️ Testing Enhanced Skill Categorization")
    print("=" * 60)
    
    # Sample skills data (simulating extracted skills)
    sample_skills_data = {
        'python': {'years': 6, 'confidence': 0.95, 'source': 'pattern_match'},
        'javascript': {'years': 5, 'confidence': 0.90, 'source': 'pattern_match'},
        'typescript': {'years': 3, 'confidence': 0.85, 'source': 'pattern_match'},
        'java': {'years': 4, 'confidence': 0.88, 'source': 'pattern_match'},
        'react': {'years': 4, 'confidence': 0.92, 'source': 'pattern_match'},
        'angular': {'years': 2, 'confidence': 0.80, 'source': 'pattern_match'},
        'django': {'years': 5, 'confidence': 0.90, 'source': 'pattern_match'},
        'flask': {'years': 3, 'confidence': 0.85, 'source': 'pattern_match'},
        'spring': {'years': 2, 'confidence': 0.78, 'source': 'pattern_match'},
        'postgresql': {'years': 5, 'confidence': 0.87, 'source': 'pattern_match'},
        'mongodb': {'years': 3, 'confidence': 0.82, 'source': 'pattern_match'},
        'redis': {'years': 2, 'confidence': 0.75, 'source': 'pattern_match'},
        'aws': {'years': 4, 'confidence': 0.89, 'source': 'pattern_match'},
        'azure': {'years': 2, 'confidence': 0.76, 'source': 'pattern_match'},
        'docker': {'years': 3, 'confidence': 0.85, 'source': 'pattern_match'},
        'kubernetes': {'years': 2, 'confidence': 0.80, 'source': 'pattern_match'},
        'jenkins': {'years': 3, 'confidence': 0.83, 'source': 'pattern_match'},
        'git': {'years': 6, 'confidence': 0.95, 'source': 'pattern_match'},
        'linux': {'years': 5, 'confidence': 0.88, 'source': 'pattern_match'},
        'tensorflow': {'years': 2, 'confidence': 0.78, 'source': 'pattern_match'},
        'pandas': {'years': 4, 'confidence': 0.86, 'source': 'pattern_match'},
        'numpy': {'years': 4, 'confidence': 0.84, 'source': 'pattern_match'},
        'pytest': {'years': 3, 'confidence': 0.81, 'source': 'pattern_match'},
        'jest': {'years': 2, 'confidence': 0.77, 'source': 'pattern_match'},
    }
    
    # Initialize skills extractor
    extractor = SkillsExtractor()
    config = Config()
    
    # Categorize skills
    categorized_skills = extractor.categorize_skills(sample_skills_data)
    
    print("\n📊 SKILL CATEGORIZATION RESULTS:")
    print("=" * 60)
    
    # Define display names for categories
    category_display_names = {
        'programming_languages': '💻 Programming Languages',
        'web_frameworks': '🌐 Web Frameworks',
        'mobile_frameworks': '📱 Mobile Frameworks',
        'data_science_ml': '🤖 Data Science & ML',
        'databases': '🗄️ Databases',
        'cloud_platforms': '☁️ Cloud Platforms',
        'devops_tools': '🔧 DevOps Tools',
        'development_tools': '⚙️ Development Tools',
        'testing_frameworks': '🧪 Testing Frameworks',
        'big_data': '📊 Big Data',
        'other': '🔹 Other Skills'
    }
    
    # Sort categories by number of skills
    sorted_categories = sorted(
        categorized_skills.items(),
        key=lambda x: len(x[1]),
        reverse=True
    )
    
    total_skills = sum(len(skills) for skills in categorized_skills.values())
    print(f"Total Skills Categorized: {total_skills}")
    print()
    
    # Display each category
    for category, skills in sorted_categories:
        if skills:  # Only show categories with skills
            display_name = category_display_names.get(category, f"🔹 {category.replace('_', ' ').title()}")
            print(f"{display_name} ({len(skills)} skills):")
            
            # Sort skills within category by experience + confidence
            sorted_skills = sorted(
                skills,
                key=lambda x: (
                    (x.get('years', 0) or 0) * 0.6 + 
                    (x.get('confidence', 0) or 0) * 0.4
                ),
                reverse=True
            )
            
            # Calculate category statistics
            avg_years = sum(skill.get('years', 0) or 0 for skill in skills) / len(skills)
            avg_confidence = sum(skill.get('confidence', 0) for skill in skills) / len(skills)
            
            print(f"  📈 Category Stats: {avg_years:.1f} years avg, {avg_confidence:.2f} confidence avg")
            print(f"  🏆 Top Skills:")
            
            for skill in sorted_skills:
                years = skill.get('years', 0) or 0
                confidence = skill.get('confidence', 0) or 0
                years_text = f"{years}y" if years > 0 else "N/A"
                print(f"    • {skill['skill']:<18} | {years_text:<4} | Confidence: {confidence:.2f}")
            
            print()
    
    # Show category strength analysis
    print("🎯 CATEGORY STRENGTH ANALYSIS:")
    print("=" * 60)
    
    category_strengths = []
    for category, skills in categorized_skills.items():
        if skills:
            avg_years = sum(skill.get('years', 0) or 0 for skill in skills) / len(skills)
            avg_confidence = sum(skill.get('confidence', 0) for skill in skills) / len(skills)
            # Strength score: count (30%) + avg_years (40%) + avg_confidence (30%)
            strength_score = (len(skills) * 0.3) + (avg_years * 0.4) + (avg_confidence * 0.3)
            
            category_strengths.append({
                'category': category,
                'display_name': category_display_names.get(category, category.replace('_', ' ').title()),
                'count': len(skills),
                'avg_years': avg_years,
                'avg_confidence': avg_confidence,
                'strength_score': strength_score
            })
    
    # Sort by strength score
    category_strengths.sort(key=lambda x: x['strength_score'], reverse=True)
    
    print("Ranking by overall strength (skills count + experience + confidence):")
    print()
    
    for i, cat in enumerate(category_strengths, 1):
        strength_level = "🟢 Strong" if cat['strength_score'] >= 5 else "🟡 Moderate" if cat['strength_score'] >= 2 else "🔴 Basic"
        print(f"{i:2d}. {cat['display_name']:<25} | {cat['count']:2d} skills | {cat['avg_years']:.1f}y avg | {strength_level}")
    
    print()
    
    # Show available skill categories from config
    print("📋 ALL AVAILABLE SKILL CATEGORIES:")
    print("=" * 60)
    
    for category, skills_list in config.SOFTWARE_SKILLS.items():
        display_name = category_display_names.get(category, category.replace('_', ' ').title())
        found_count = len(categorized_skills.get(category, []))
        total_count = len(skills_list)
        coverage = (found_count / total_count) * 100 if total_count > 0 else 0
        
        print(f"{display_name:<25} | {found_count:2d}/{total_count:2d} skills | {coverage:5.1f}% coverage")
        
        # Show some example skills from this category
        example_skills = skills_list[:5]
        print(f"  Examples: {', '.join(example_skills)}")
        print()

def show_role_specific_categorization():
    """Show how skills are weighted for different roles."""
    print("\n🎯 ROLE-SPECIFIC SKILL WEIGHTING:")
    print("=" * 60)
    
    config = Config()
    
    # Show skill weights for different roles
    roles_to_show = ['data_scientist', 'full_stack_developer', 'devops_engineer']
    
    for role in roles_to_show:
        role_info = config.ROLE_SKILL_MAPPING.get(role, {})
        skill_weights = role_info.get('skill_weights', {})
        
        print(f"\n{role.replace('_', ' ').title()}:")
        print("  Category importance (weights):")
        
        # Sort by weight (importance)
        sorted_weights = sorted(skill_weights.items(), key=lambda x: x[1], reverse=True)
        
        for category, weight in sorted_weights:
            display_name = category.replace('_', ' ').title()
            importance = "🔥 Critical" if weight >= 0.3 else "⭐ Important" if weight >= 0.2 else "📝 Relevant"
            print(f"    {display_name:<20} | {weight:4.0%} | {importance}")

if __name__ == "__main__":
    test_skill_categorization()
    show_role_specific_categorization()
    
    print("\n" + "="*60)
    print("✅ Skill categorization test completed!")
    print("💡 Run 'python main.py your_resume.pdf' to see categorized skills in action!")
