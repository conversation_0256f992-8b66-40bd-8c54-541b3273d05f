# Core dependencies for resume screening application
PyPDF2==3.0.1
pdfplumber==0.10.0
transformers==4.36.0
torch==2.1.0
spacy==3.7.2
pandas==2.1.4
python-dotenv==1.0.0
numpy==1.24.3
scikit-learn==1.3.2
regex==2023.10.3

# For enhanced AI models and semantic analysis
sentence-transformers==2.2.2
huggingface-hub==0.19.4

# Optional: For advanced NLP processing
nltk==3.8.1

# For API integration (if needed later)
requests==2.31.0

# For testing
pytest==7.4.3
pytest-cov==4.1.0

# For better text processing and analysis
textstat==0.7.3
wordcloud==1.9.2
