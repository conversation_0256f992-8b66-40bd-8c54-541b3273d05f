# Core dependencies for resume screening application
PyPDF2==3.0.1
pdfplumber==0.10.0
transformers==4.36.0
torch==2.1.0
spacy==3.7.2
pandas==2.1.4
python-dotenv==1.0.0
numpy==1.24.3
scikit-learn==1.3.2
regex==2023.10.3

# For enhanced AI models and semantic analysis
sentence-transformers==2.2.2
huggingface-hub==0.19.4

# Optional: For advanced NLP processing
nltk==3.8.1

# For API integration (if needed later)
requests==2.31.0

# For testing
pytest==7.4.3
pytest-cov==4.1.0

# For better text processing and analysis
textstat==0.7.3
wordcloud==1.9.2

# Advanced PDF parsing and OCR
pymupdf==1.23.14
pytesseract==0.3.10
Pillow==10.1.0
pdf2image==1.16.3

# Alternative PDF parsers
pdfminer.six==20231228
camelot-py[cv]==0.11.0

# For AI-powered text extraction and understanding
openai==1.6.1
anthropic==0.8.1
