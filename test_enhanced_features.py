#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced resume screening features.
"""
import json
from src.resume_screener import ResumeScreener
from src.comprehensive_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_comprehensive_analysis():
    """Test the comprehensive analysis features with sample data."""
    
    # Sample resume text for testing
    sample_text = """
    <PERSON>
    Senior Software Engineer
    <EMAIL> | (555) 123-4567 | San Francisco, CA
    LinkedIn: linkedin.com/in/johndoe | GitHub: github.com/johndoe
    
    PROFESSIONAL SUMMARY
    Experienced software engineer with 8 years of experience in full-stack development,
    specializing in Python, React, and cloud technologies. Led multiple teams and 
    delivered scalable web applications serving millions of users.
    
    TECHNICAL SKILLS
    • Programming Languages: Python (6 years), JavaScript (5 years), TypeScript (3 years), Java (4 years)
    • Frontend: React (4 years), Angular (2 years), HTML/CSS (6 years)
    • Backend: Django (5 years), Flask (3 years), Node.js (3 years), Spring Boot (2 years)
    • Databases: PostgreSQL (5 years), MongoDB (3 years), Red<PERSON> (2 years)
    • Cloud: A<PERSON> (4 years), <PERSON><PERSON> (3 years), Kubernetes (2 years)
    • Tools: G<PERSON>, <PERSON>, JIRA, Terraform
    
    EXPERIENCE
    Senior Software Engineer | TechCorp | 2020-Present
    • Led a team of 5 developers in building microservices architecture
    • Implemented CI/CD pipelines reducing deployment time by 60%
    • Designed and developed RESTful APIs serving 10M+ requests daily
    
    Software Engineer | StartupXYZ | 2018-2020
    • Developed full-stack web applications using React and Django
    • Optimized database queries improving performance by 40%
    • Collaborated with product team on feature development
    
    EDUCATION
    Bachelor of Science in Computer Science | University of California | 2016
    
    CERTIFICATIONS
    • AWS Certified Solutions Architect
    • Certified Kubernetes Administrator
    """
    
    # Initialize analyzer
    analyzer = ComprehensiveAnalyzer()
    
    print("🧪 Testing Enhanced Resume Analysis Features")
    print("=" * 60)
    
    # Test personal information extraction
    print("\n1. Testing Personal Information Extraction...")
    personal_info = analyzer.extract_personal_information(sample_text)
    print(f"   Name: {personal_info.get('name')}")
    print(f"   Email: {personal_info.get('email')}")
    print(f"   Phone: {personal_info.get('phone')}")
    print(f"   Location: {personal_info.get('location')}")
    print(f"   LinkedIn: {personal_info.get('linkedin')}")
    print(f"   GitHub: {personal_info.get('github')}")
    
    # Sample skills data for testing
    sample_skills = {
        'python': {'years': 6, 'confidence': 0.95, 'source': 'pattern_match'},
        'javascript': {'years': 5, 'confidence': 0.90, 'source': 'pattern_match'},
        'react': {'years': 4, 'confidence': 0.88, 'source': 'pattern_match'},
        'django': {'years': 5, 'confidence': 0.92, 'source': 'pattern_match'},
        'postgresql': {'years': 5, 'confidence': 0.85, 'source': 'pattern_match'},
        'aws': {'years': 4, 'confidence': 0.87, 'source': 'pattern_match'},
        'docker': {'years': 3, 'confidence': 0.83, 'source': 'pattern_match'},
        'kubernetes': {'years': 2, 'confidence': 0.80, 'source': 'pattern_match'},
        'typescript': {'years': 3, 'confidence': 0.85, 'source': 'pattern_match'},
        'mongodb': {'years': 3, 'confidence': 0.82, 'source': 'pattern_match'}
    }
    
    # Test role analysis
    print("\n2. Testing Role Fit Analysis...")
    role_analysis = analyzer.analyze_role_fit(sample_skills, sample_text)
    best_fit = role_analysis.get('best_fit_role', 'N/A').replace('_', ' ').title()
    print(f"   Best Fit Role: {best_fit}")
    
    top_3 = role_analysis.get('top_3_roles', [])
    print("   Top 3 Role Matches:")
    for i, (role, score) in enumerate(top_3[:3], 1):
        role_name = role.replace('_', ' ').title()
        detailed = role_analysis.get('detailed_analysis', {}).get(role, {})
        fit_percentage = detailed.get('fit_percentage', 0)
        print(f"     {i}. {role_name}: {fit_percentage:.1f}% fit (Score: {score:.1f})")
    
    # Test skill combinations
    print("\n3. Testing Skill Combination Analysis...")
    skill_combinations = analyzer.analyze_skill_combinations(sample_skills)
    tech_stacks = skill_combinations.get('technology_stacks', [])
    print("   Technology Stack Completeness:")
    for stack in tech_stacks[:3]:
        print(f"     • {stack['stack']}: {stack['completeness']:.0f}% complete ({stack['experience_level']})")
    
    # Test comprehensive report
    print("\n4. Testing Comprehensive Report Generation...")
    additional_info = {
        'total_experience': 8,
        'education': ['Bachelor of Science in Computer Science'],
        'certifications': ['AWS Certified Solutions Architect', 'Certified Kubernetes Administrator']
    }
    
    comprehensive_report = analyzer.generate_detailed_report(
        personal_info, role_analysis, skill_combinations, sample_skills, additional_info
    )
    
    # Display key metrics
    marketability = comprehensive_report.get('overall_assessment', {}).get('marketability_score', {})
    print(f"   Marketability Score: {marketability.get('overall_score', 0):.1f}/100")
    print(f"   Grade: {marketability.get('grade', 'N/A')}")
    print(f"   Market Readiness: {marketability.get('market_readiness', 'N/A').title()}")
    
    # Show recommendations
    recommendations = comprehensive_report.get('recommendations', {})
    immediate_actions = recommendations.get('immediate_actions', [])
    if immediate_actions:
        print("\n   Immediate Recommendations:")
        for i, action in enumerate(immediate_actions, 1):
            print(f"     {i}. {action}")
    
    # Show unique value proposition
    uvp = comprehensive_report.get('overall_assessment', {}).get('unique_value_proposition', '')
    if uvp:
        print(f"\n   Unique Value Proposition: {uvp}")
    
    print("\n✅ Enhanced features test completed successfully!")
    print("=" * 60)
    
    return {
        'personal_info': personal_info,
        'role_analysis': role_analysis,
        'skill_combinations': skill_combinations,
        'comprehensive_report': comprehensive_report
    }

def demonstrate_role_specific_analysis():
    """Demonstrate role-specific skill analysis."""
    print("\n🎯 Role-Specific Skill Requirements Demo")
    print("=" * 60)
    
    from config import Config
    config = Config()
    
    # Show role mappings
    for role, requirements in list(config.ROLE_SKILL_MAPPING.items())[:3]:
        role_name = role.replace('_', ' ').title()
        print(f"\n{role_name}:")
        print(f"  Required Skills: {', '.join(requirements['required_skills'][:5])}")
        print(f"  Preferred Skills: {', '.join(requirements['preferred_skills'][:5])}")
        print(f"  Min Experience: {requirements['min_experience']} years")
        print(f"  Key Keywords: {', '.join(requirements['keywords'][:3])}")

if __name__ == "__main__":
    # Run the comprehensive test
    test_results = test_comprehensive_analysis()
    
    # Demonstrate role-specific analysis
    demonstrate_role_specific_analysis()
    
    # Save test results
    with open('test_results.json', 'w') as f:
        json.dump(test_results, f, indent=2, default=str)
    
    print(f"\n💾 Test results saved to 'test_results.json'")
    print("🎉 All enhanced features are working correctly!")
