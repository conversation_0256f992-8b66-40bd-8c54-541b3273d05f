{"file_path": "Hoang Phan CV.pdf", "timestamp": "2025-06-23T20:58:20.494635", "success": true, "error": null, "extraction_method": "pdfplumber", "raw_text": "HOANG PHAN\nCONTACT PROFILE\nResults-driven and innovative Lead Data Engineer with over 10 years\n+**************\nof experience designing, building, and optimizing data platforms and\nmachine learning solutions from the ground up. Proven expertise in\nhoangds121@gmail,com\nbig data technologies (Spark, Airflow, BigQuery), cloud infrastructure,\nand applying NLP and sentiment analysis to solve complex business\nDistrict 7, Ho Chi Minh City\nproblems. Seeking to leverage deep technical knowledge to drive data\ninnovation in a challenging senior engineering role.\nEDUCATION WORK EXPERIENCE\nTamara 2021 - NOW\nBachelor of Software Engineering\nLead Data Engineer\nUniversity of Science (HCMUS) Build an AI platform that supports building LLM applications.\nMigrate our data platform from OCI to GCP with limited downtime.\nSKILLS Engineered and launched a comprehensive data platform from\nscratch (DataLake, DataMarts) using open-source technologies,\nprocessing over 1TB of data daily to support critical business\nLanguages\nintelligence.\nPython Automated data migration from legacy systems to Google\nBigQuery using a CDC solution, ensuring zero downtime and\nJava\nimproving data accessibility for the analytics team.\nJavascript\nPartnered with the Data Analytics team to build and optimize\nSQL mission-critical dashboards and reports for the Board of Directors,\nleading to a 20% faster decision-making cycle.\nBig Data & Cloud\nGlobal Fashion Group 2020 - 2021\nSpark\nSenior Data Engineer\nHDFS\nMaintained and scaled data infrastructure, developing new ETL\nAirflow\npipelines with Airflow and EMR to ingest data from multiple global\nGoogle BigQuery regions into a centralized data lake.\nOptimized data processing jobs, reducing execution time by 30%\nAWS EMR\nand saving significant cloud computing costs.\nAWS Redshift\nADIA - Lead Data Engineer 2018 - 2020\nHive Lead Data Engineer\nTerraform\nLed the design and development of a text-mining platform for\nDocker trade surveillance, processing thousands of chat logs daily to\ndetect compliance faults and fraudulent activity.\nExtracted and structured critical transaction information from\nunstructured chat data, improving the efficiency of the compliance\nteam by 40%.\nZalora\nLANGUAGES Senior Data Engineer 2016 - 2018\nData Engineer 2015 - 2016\nVietnamese\nDeveloped and integrated a personalized search solution using\nEnglish (Fluent)\nLearning to Rank (LTR) models into SOLR, increasing search\nconversion rates by 15%.\nEngineered a machine learning system for the automated approval\nof customer reviews using state-of-the-art NLP models (ULMFiT,\nBERT), reducing manual moderation workload by 75%.\nBuilt and deployed an item-item recommendation engine for email\nand web platforms, which increased user engagement and drove a\n10% uplift in cross-sell revenue.\nDesigned and constructed a scalable Data Science Platform using\nMesos, Spark, and HDFS, empowering the data science team to\nexperiment and deploy models more efficiently.\nTinyPulse 2014 - 2015\nData Engineer\nDeveloped a sentiment analysis engine to process employee\nfeedback, providing actionable insights into company culture and\nperformance across key dimensions.\nCreated custom, data-driven reports for enterprise customers,\nenhancing client satisfaction and retention.\nAtlassian 2014 - 2014\nSenior Developer\nOperated in a hybrid growth developer and data analyst role,\ndesigning and implementing A/B tests to validate new product\nhypotheses and evaluate feature impact.\nQueried and analyzed large datasets from Redshift and Hive to\nuncover user behavior patterns, presenting findings through\nvisualizations to inform product strategy.\nMobivi 2011 - 2014\nSenior Developer\nArchitected and developed key components of the MCA E-\nCommerce platform, including the inventory management, order\norchestration, and fulfillment systems.\nBuilt a distributed web crawling system to collect and process\nonline product information from competitor websites, supporting\ncompetitive pricing strategies.\nTMA Solution 2010 - 2011\nJunior Developer\nContributed to the development of the NECA project, a network\nmonitoring solution for fiber optic cable infrastructure.", "entities": [{"entity_group": "ORG", "score": 0.8846606016159058, "word": "Spark", "start": 290, "end": 295}, {"entity_group": "ORG", "score": 0.8468056321144104, "word": "Airflow", "start": 297, "end": 304}, {"entity_group": "ORG", "score": 0.7088140249252319, "word": "<PERSON><PERSON><PERSON><PERSON>", "start": 306, "end": 314}, {"entity_group": "LOC", "score": 0.9989463090896606, "word": "Ho Chi Minh City", "start": 417, "end": 433}, {"entity_group": "ORG", "score": 0.7204735279083252, "word": "Software Engineering", "start": 610, "end": 630}, {"entity_group": "ORG", "score": 0.8562830090522766, "word": "Data Engineer University of Science", "start": 636, "end": 671}, {"entity_group": "ORG", "score": 0.8111494183540344, "word": "HCMUS", "start": 673, "end": 678}, {"entity_group": "ORG", "score": 0.8210078477859497, "word": "OCI", "start": 772, "end": 775}, {"entity_group": "ORG", "score": 0.8009862899780273, "word": "SKIL", "start": 805, "end": 809}, {"entity_group": "ORG", "score": 0.7738878726959229, "word": "DataLake", "start": 880, "end": 888}, {"entity_group": "MISC", "score": 0.960641086101532, "word": "Python", "start": 1019, "end": 1025}, {"entity_group": "MISC", "score": 0.7909544110298157, "word": "Big", "start": 1081, "end": 1084}, {"entity_group": "ORG", "score": 0.9363207221031189, "word": "CDC", "start": 1098, "end": 1101}, {"entity_group": "MISC", "score": 0.9805293679237366, "word": "Java", "start": 1139, "end": 1143}, {"entity_group": "MISC", "score": 0.986962616443634, "word": "Java", "start": 1196, "end": 1200}, {"entity_group": "ORG", "score": 0.9914495944976807, "word": "Data Analytics", "start": 1226, "end": 1240}, {"entity_group": "MISC", "score": 0.7767922878265381, "word": "SQL", "start": 1268, "end": 1271}, {"entity_group": "ORG", "score": 0.7007539868354797, "word": "Board of Directors", "start": 1320, "end": 1338}, {"entity_group": "ORG", "score": 0.9102935791015625, "word": "Big Data & Cloud Global Fashion Group", "start": 1386, "end": 1423}, {"entity_group": "ORG", "score": 0.9032857418060303, "word": "##rk", "start": 1439, "end": 1441}, {"entity_group": "ORG", "score": 0.9819849133491516, "word": "HDFS", "start": 1463, "end": 1467}, {"entity_group": "MISC", "score": 0.8771641850471497, "word": "Big", "start": 1612, "end": 1615}, {"entity_group": "ORG", "score": 0.9930092096328735, "word": "Terra", "start": 1849, "end": 1854}, {"entity_group": "ORG", "score": 0.8907542824745178, "word": "Zalora LANGUAGES", "start": 142, "end": 158}, {"entity_group": "MISC", "score": 0.9982784986495972, "word": "Vietnamese", "start": 218, "end": 228}, {"entity_group": "MISC", "score": 0.9987006187438965, "word": "English", "start": 291, "end": 298}, {"entity_group": "MISC", "score": 0.789925754070282, "word": "L", "start": 326, "end": 327}, {"entity_group": "ORG", "score": 0.9234862327575684, "word": "Spark", "start": 799, "end": 804}, {"entity_group": "ORG", "score": 0.9182242751121521, "word": "HDFS", "start": 810, "end": 814}, {"entity_group": "ORG", "score": 0.7757359743118286, "word": "Engineer", "start": 925, "end": 933}, {"entity_group": "MISC", "score": 0.9669904708862305, "word": "Atlassian", "start": 1197, "end": 1206}, {"entity_group": "ORG", "score": 0.9763943552970886, "word": "MCA", "start": 1645, "end": 1648}, {"entity_group": "MISC", "score": 0.7143715620040894, "word": "E", "start": 1649, "end": 1650}], "skills": {"python": {"years": null, "confidence": 1.0, "context": "support critical business\nLanguages\nintelligence.\nPython Automated data migration from legacy systems to G", "source": "pattern_match"}, "java": {"years": null, "confidence": 1.0, "context": "using a CDC solution, ensuring zero downtime and\nJava\nimproving data accessibility for the analytics te", "source": "pattern_match"}, "javascript": {"years": null, "confidence": 1.0, "context": "roving data accessibility for the analytics team.\nJavascript\nPartnered with the Data Analytics team to build a", "source": "pattern_match"}, "terraform": {"years": null, "confidence": 1.0, "context": "Data Engineer 2018 - 2020\nHive Lead Data Engineer\nTerra<PERSON>\nLed the design and development of a text-mining p", "source": "pattern_match"}, "docker": {"years": null, "confidence": 1.0, "context": "ign and development of a text-mining platform for\nDocker trade surveillance, processing thousands of chat", "source": "pattern_match"}, "aws": {"years": null, "confidence": 1.0, "context": "a processing jobs, reducing execution time by 30%\nAWS EMR\nand saving significant cloud computing costs.", "source": "pattern_match"}, "gcp": {"years": null, "confidence": 1.0, "context": "plications.\nMigrate our data platform from OCI to GCP with limited downtime.\nSKILLS Engineered and laun", "source": "pattern_match"}, "r": {"years": 10, "confidence": 0.9930092096328735, "context": "Terra", "source": "ner_entity"}, "mysql": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "postgresql": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "sqlite": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "kotlin": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "scala": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "matlab": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "angular": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "flask": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "laravel": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "rails": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "tensorflow": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "elasticsearch": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "oracle": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "google cloud": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "ansible": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "confluence": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "slack": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "linux": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "powershell": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "typescript": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "react": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "vue": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "express": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "asp.net": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "keras": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "redis": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "azure": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "kubernetes": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "jenkins": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}}, "categorized_skills": {"programming_languages": [{"skill": "python", "years": null, "confidence": 1.0, "context": "support critical business\nLanguages\nintelligence.\nPython Automated data migration from legacy systems to G", "source": "pattern_match"}, {"skill": "java", "years": null, "confidence": 1.0, "context": "using a CDC solution, ensuring zero downtime and\nJava\nimproving data accessibility for the analytics te", "source": "pattern_match"}, {"skill": "javascript", "years": null, "confidence": 1.0, "context": "roving data accessibility for the analytics team.\nJavascript\nPartnered with the Data Analytics team to build a", "source": "pattern_match"}, {"skill": "r", "years": 10, "confidence": 0.9930092096328735, "context": "Terra", "source": "ner_entity"}, {"skill": "kotlin", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "scala", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "matlab", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "typescript", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "cloud_platforms": [{"skill": "terraform", "years": null, "confidence": 1.0, "context": "Data Engineer 2018 - 2020\nHive Lead Data Engineer\nTerra<PERSON>\nLed the design and development of a text-mining p", "source": "pattern_match"}, {"skill": "docker", "years": null, "confidence": 1.0, "context": "ign and development of a text-mining platform for\nDocker trade surveillance, processing thousands of chat", "source": "pattern_match"}, {"skill": "aws", "years": null, "confidence": 1.0, "context": "a processing jobs, reducing execution time by 30%\nAWS EMR\nand saving significant cloud computing costs.", "source": "pattern_match"}, {"skill": "gcp", "years": null, "confidence": 1.0, "context": "plications.\nMigrate our data platform from OCI to GCP with limited downtime.\nSKILLS Engineered and laun", "source": "pattern_match"}, {"skill": "google cloud", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "ansible", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "azure", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "kubernetes", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "databases": [{"skill": "mysql", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "postgresql", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "sqlite", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "elasticsearch", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "oracle", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "redis", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "frameworks": [{"skill": "angular", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "flask", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "laravel", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "rails", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "tensorflow", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "react", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "vue", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "express", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "asp.net", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "keras", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "tools": [{"skill": "confluence", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "slack", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "linux", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "powershell", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "jenkins", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}]}, "additional_info": {"total_experience": null, "education": ["bachelor of software engineering", "ms and", "machine learning solutions from the ground up", "mail,com", "ms. seeking to leverage deep technical knowledge to drive data", "mara 2021 - now", "bachelor of software engineering", "marts) using open-source technologies,", "mated data migration from legacy systems to google", "making cycle", "bal fashion group 2020 - 2021", "maintained and scaled data infrastructure, developing new etl", "bs, reducing execution time by 30%", "mation from", "machine learning system for the automated approval", "manual moderation workload by 75%", "ms, which increased user engagement and drove a", "back, providing actionable insights into company culture and", "mance across key dimensions", "management, order", "ms.\nbuilt a distributed web crawling system to collect and process", "mation from competitor websites, supporting", "ma solution 2010 - 2011"], "certifications": ["aws emr", "aws redshift"], "projects": [], "contact_info": {}}, "summary": {"total_skills_found": 37, "skills_by_category": {"programming_languages": 8, "cloud_platforms": 8, "databases": 6, "frameworks": 10, "tools": 5}, "category_scores": {"programming_languages": 1.0087709054350853, "cloud_platforms": 0.8760743290185928, "databases": 0.7773333887259165, "frameworks": 0.7521486580371857, "tools": 0.7748149156570434}, "top_skills": [{"skill": "r", "years": 10, "confidence": 0.9930092096328735, "context": "Terra", "source": "ner_entity"}, {"skill": "python", "years": null, "confidence": 1.0, "context": "support critical business\nLanguages\nintelligence.\nPython Automated data migration from legacy systems to G", "source": "pattern_match"}, {"skill": "java", "years": null, "confidence": 1.0, "context": "using a CDC solution, ensuring zero downtime and\nJava\nimproving data accessibility for the analytics te", "source": "pattern_match"}, {"skill": "javascript", "years": null, "confidence": 1.0, "context": "roving data accessibility for the analytics team.\nJavascript\nPartnered with the Data Analytics team to build a", "source": "pattern_match"}, {"skill": "terraform", "years": null, "confidence": 1.0, "context": "Data Engineer 2018 - 2020\nHive Lead Data Engineer\nTerra<PERSON>\nLed the design and development of a text-mining p", "source": "pattern_match"}, {"skill": "docker", "years": null, "confidence": 1.0, "context": "ign and development of a text-mining platform for\nDocker trade surveillance, processing thousands of chat", "source": "pattern_match"}, {"skill": "aws", "years": null, "confidence": 1.0, "context": "a processing jobs, reducing execution time by 30%\nAWS EMR\nand saving significant cloud computing costs.", "source": "pattern_match"}, {"skill": "gcp", "years": null, "confidence": 1.0, "context": "plications.\nMigrate our data platform from OCI to GCP with limited downtime.\nSKILLS Engineered and laun", "source": "pattern_match"}, {"skill": "kotlin", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "scala", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}], "total_experience_years": null, "education_count": 23, "certifications_count": 2, "has_contact_info": false, "strongest_category": "programming_languages"}}