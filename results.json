{"file_path": "Hoang Phan CV.pdf", "timestamp": "2025-06-23T21:10:38.762378", "success": true, "error": null, "extraction_method": "pdfplumber", "raw_text": "HOANG PHAN\nCONTACT PROFILE\nResults-driven and innovative Lead Data Engineer with over 10 years\n+**************\nof experience designing, building, and optimizing data platforms and\nmachine learning solutions from the ground up. Proven expertise in\nhoangds121@gmail,com\nbig data technologies (Spark, Airflow, BigQuery), cloud infrastructure,\nand applying NLP and sentiment analysis to solve complex business\nDistrict 7, Ho Chi Minh City\nproblems. Seeking to leverage deep technical knowledge to drive data\ninnovation in a challenging senior engineering role.\nEDUCATION WORK EXPERIENCE\nTamara 2021 - NOW\nBachelor of Software Engineering\nLead Data Engineer\nUniversity of Science (HCMUS) Build an AI platform that supports building LLM applications.\nMigrate our data platform from OCI to GCP with limited downtime.\nSKILLS Engineered and launched a comprehensive data platform from\nscratch (DataLake, DataMarts) using open-source technologies,\nprocessing over 1TB of data daily to support critical business\nLanguages\nintelligence.\nPython Automated data migration from legacy systems to Google\nBigQuery using a CDC solution, ensuring zero downtime and\nJava\nimproving data accessibility for the analytics team.\nJavascript\nPartnered with the Data Analytics team to build and optimize\nSQL mission-critical dashboards and reports for the Board of Directors,\nleading to a 20% faster decision-making cycle.\nBig Data & Cloud\nGlobal Fashion Group 2020 - 2021\nSpark\nSenior Data Engineer\nHDFS\nMaintained and scaled data infrastructure, developing new ETL\nAirflow\npipelines with Airflow and EMR to ingest data from multiple global\nGoogle BigQuery regions into a centralized data lake.\nOptimized data processing jobs, reducing execution time by 30%\nAWS EMR\nand saving significant cloud computing costs.\nAWS Redshift\nADIA - Lead Data Engineer 2018 - 2020\nHive Lead Data Engineer\nTerraform\nLed the design and development of a text-mining platform for\nDocker trade surveillance, processing thousands of chat logs daily to\ndetect compliance faults and fraudulent activity.\nExtracted and structured critical transaction information from\nunstructured chat data, improving the efficiency of the compliance\nteam by 40%.\nZalora\nLANGUAGES Senior Data Engineer 2016 - 2018\nData Engineer 2015 - 2016\nVietnamese\nDeveloped and integrated a personalized search solution using\nEnglish (Fluent)\nLearning to Rank (LTR) models into SOLR, increasing search\nconversion rates by 15%.\nEngineered a machine learning system for the automated approval\nof customer reviews using state-of-the-art NLP models (ULMFiT,\nBERT), reducing manual moderation workload by 75%.\nBuilt and deployed an item-item recommendation engine for email\nand web platforms, which increased user engagement and drove a\n10% uplift in cross-sell revenue.\nDesigned and constructed a scalable Data Science Platform using\nMesos, Spark, and HDFS, empowering the data science team to\nexperiment and deploy models more efficiently.\nTinyPulse 2014 - 2015\nData Engineer\nDeveloped a sentiment analysis engine to process employee\nfeedback, providing actionable insights into company culture and\nperformance across key dimensions.\nCreated custom, data-driven reports for enterprise customers,\nenhancing client satisfaction and retention.\nAtlassian 2014 - 2014\nSenior Developer\nOperated in a hybrid growth developer and data analyst role,\ndesigning and implementing A/B tests to validate new product\nhypotheses and evaluate feature impact.\nQueried and analyzed large datasets from Redshift and Hive to\nuncover user behavior patterns, presenting findings through\nvisualizations to inform product strategy.\nMobivi 2011 - 2014\nSenior Developer\nArchitected and developed key components of the MCA E-\nCommerce platform, including the inventory management, order\norchestration, and fulfillment systems.\nBuilt a distributed web crawling system to collect and process\nonline product information from competitor websites, supporting\ncompetitive pricing strategies.\nTMA Solution 2010 - 2011\nJunior Developer\nContributed to the development of the NECA project, a network\nmonitoring solution for fiber optic cable infrastructure.", "entities": [{"entity_group": "ORG", "score": 0.8846606016159058, "word": "Spark", "start": 290, "end": 295}, {"entity_group": "ORG", "score": 0.8468056321144104, "word": "Airflow", "start": 297, "end": 304}, {"entity_group": "ORG", "score": 0.7088140249252319, "word": "<PERSON><PERSON><PERSON><PERSON>", "start": 306, "end": 314}, {"entity_group": "LOC", "score": 0.9989463090896606, "word": "Ho Chi Minh City", "start": 417, "end": 433}, {"entity_group": "ORG", "score": 0.7204735279083252, "word": "Software Engineering", "start": 610, "end": 630}, {"entity_group": "ORG", "score": 0.8562830090522766, "word": "Data Engineer University of Science", "start": 636, "end": 671}, {"entity_group": "ORG", "score": 0.8111494183540344, "word": "HCMUS", "start": 673, "end": 678}, {"entity_group": "ORG", "score": 0.8210078477859497, "word": "OCI", "start": 772, "end": 775}, {"entity_group": "ORG", "score": 0.8009862899780273, "word": "SKIL", "start": 805, "end": 809}, {"entity_group": "ORG", "score": 0.7738878726959229, "word": "DataLake", "start": 880, "end": 888}, {"entity_group": "MISC", "score": 0.960641086101532, "word": "Python", "start": 1019, "end": 1025}, {"entity_group": "MISC", "score": 0.7909544110298157, "word": "Big", "start": 1081, "end": 1084}, {"entity_group": "ORG", "score": 0.9363207221031189, "word": "CDC", "start": 1098, "end": 1101}, {"entity_group": "MISC", "score": 0.9805293679237366, "word": "Java", "start": 1139, "end": 1143}, {"entity_group": "MISC", "score": 0.986962616443634, "word": "Java", "start": 1196, "end": 1200}, {"entity_group": "ORG", "score": 0.9914495944976807, "word": "Data Analytics", "start": 1226, "end": 1240}, {"entity_group": "MISC", "score": 0.7767922878265381, "word": "SQL", "start": 1268, "end": 1271}, {"entity_group": "ORG", "score": 0.7007539868354797, "word": "Board of Directors", "start": 1320, "end": 1338}, {"entity_group": "ORG", "score": 0.9102935791015625, "word": "Big Data & Cloud Global Fashion Group", "start": 1386, "end": 1423}, {"entity_group": "ORG", "score": 0.9032857418060303, "word": "##rk", "start": 1439, "end": 1441}, {"entity_group": "ORG", "score": 0.9819849133491516, "word": "HDFS", "start": 1463, "end": 1467}, {"entity_group": "MISC", "score": 0.8771641850471497, "word": "Big", "start": 1612, "end": 1615}, {"entity_group": "ORG", "score": 0.9930092096328735, "word": "Terra", "start": 1849, "end": 1854}, {"entity_group": "ORG", "score": 0.8907542824745178, "word": "Zalora LANGUAGES", "start": 142, "end": 158}, {"entity_group": "MISC", "score": 0.9982784986495972, "word": "Vietnamese", "start": 218, "end": 228}, {"entity_group": "MISC", "score": 0.9987006187438965, "word": "English", "start": 291, "end": 298}, {"entity_group": "MISC", "score": 0.789925754070282, "word": "L", "start": 326, "end": 327}, {"entity_group": "ORG", "score": 0.9234862327575684, "word": "Spark", "start": 799, "end": 804}, {"entity_group": "ORG", "score": 0.9182242751121521, "word": "HDFS", "start": 810, "end": 814}, {"entity_group": "ORG", "score": 0.7757359743118286, "word": "Engineer", "start": 925, "end": 933}, {"entity_group": "MISC", "score": 0.9669904708862305, "word": "Atlassian", "start": 1197, "end": 1206}, {"entity_group": "ORG", "score": 0.9763943552970886, "word": "MCA", "start": 1645, "end": 1648}, {"entity_group": "MISC", "score": 0.7143715620040894, "word": "E", "start": 1649, "end": 1650}], "skills": {"spark": {"years": null, "confidence": 1.0, "context": "se in\nhoangds121@gmail,com\nbig data technologies (Spark, Airflow, BigQuery), cloud infrastructure,\nand ap", "source": "pattern_match"}, "airflow": {"years": null, "confidence": 1.0, "context": "oangds121@gmail,com\nbig data technologies (Spark, Airflow, BigQuery), cloud infrastructure,\nand applying NL", "source": "pattern_match"}, "bigquery": {"years": null, "confidence": 1.0, "context": "@gmail,com\nbig data technologies (Spark, Airflow, BigQuery), cloud infrastructure,\nand applying NLP and sent", "source": "pattern_match"}, "python": {"years": null, "confidence": 1.0, "context": "support critical business\nLanguages\nintelligence.\nPython Automated data migration from legacy systems to G", "source": "pattern_match"}, "java": {"years": null, "confidence": 1.0, "context": "using a CDC solution, ensuring zero downtime and\nJava\nimproving data accessibility for the analytics te", "source": "pattern_match"}, "javascript": {"years": null, "confidence": 1.0, "context": "roving data accessibility for the analytics team.\nJavascript\nPartnered with the Data Analytics team to build a", "source": "pattern_match"}, "sql": {"years": null, "confidence": 1.0, "context": "ith the Data Analytics team to build and optimize\nSQL mission-critical dashboards and reports for the B", "source": "pattern_match"}, "hdfs": {"years": null, "confidence": 1.0, "context": "hion Group 2020 - 2021\nSpark\nSenior Data Engineer\nHDFS\nMaintained and scaled data infrastructure, develo", "source": "pattern_match"}, "terraform": {"years": null, "confidence": 1.0, "context": "Data Engineer 2018 - 2020\nHive Lead Data Engineer\nTerra<PERSON>\nLed the design and development of a text-mining p", "source": "pattern_match"}, "redshift": {"years": null, "confidence": 1.0, "context": "and saving significant cloud computing costs.\nAWS Redshift\nADIA - Lead Data Engineer 2018 - 2020\nHive Lead D", "source": "pattern_match"}, "docker": {"years": null, "confidence": 1.0, "context": "ign and development of a text-mining platform for\nDocker trade surveillance, processing thousands of chat", "source": "pattern_match"}, "hive": {"years": null, "confidence": 1.0, "context": "WS Redshift\nADIA - Lead Data Engineer 2018 - 2020\nHive Lead Data Engineer\n<PERSON><PERSON>\nLed the design and d", "source": "pattern_match"}, "aws": {"years": null, "confidence": 1.0, "context": "a processing jobs, reducing execution time by 30%\nAWS EMR\nand saving significant cloud computing costs.", "source": "pattern_match"}, "gcp": {"years": null, "confidence": 1.0, "context": "plications.\nMigrate our data platform from OCI to GCP with limited downtime.\nSKILLS Engineered and laun", "source": "pattern_match"}, "r": {"years": 10, "confidence": 0.9930092096328735, "context": "Terra", "source": "ner_entity"}, "apache spark": {"years": null, "confidence": 0.9234862327575684, "context": "Spark", "source": "ner_entity"}, "mysql": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "postgresql": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "sqlite": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "kotlin": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "scala": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "matlab": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "html": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "perl": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "lua": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "haskell": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "clojure": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "angular": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "svelte": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "flask": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "laravel": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "rails": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "blazor": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "flutter": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "kotlin multiplatform": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "tensorflow": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "scikit-learn": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "matplotlib": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "plotly": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "mlflow": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "kubeflow": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "elasticsearch": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "oracle": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "influxdb": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "snowflake": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "clickhouse": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "google cloud": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "alibaba cloud": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "digitalocean": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "vercel": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "netlify": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "cloudflare": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "ansible": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "gitlab ci": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "circleci": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "helm": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "elk stack": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "new relic": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "mercurial": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "confluence": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "slack": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "linux": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "powershell": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "intellij": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "eclipse": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "selenium": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "flink": {"years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, "typescript": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "react": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "vue": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "next.js": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "express": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "asp.net": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "ember.js": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "react native": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "phonegap": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "keras": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "seaborn": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "jupyter": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "redis": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "neo4j": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "couchbase": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "azure": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "heroku": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "kubernetes": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "jenkins": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "prometheus": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "teams": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "vscode": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "swagger": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "sketch": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "jest": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "cypress": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "pytest": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "unittest": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "testng": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "cucumber": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "jasmine": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "hbase": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "zookeeper": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, "parquet": {"years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}}, "categorized_skills": {"data_science_ml": [{"skill": "spark", "years": null, "confidence": 1.0, "context": "se in\nhoangds121@gmail,com\nbig data technologies (Spark, Airflow, BigQuery), cloud infrastructure,\nand ap", "source": "pattern_match"}, {"skill": "airflow", "years": null, "confidence": 1.0, "context": "oangds121@gmail,com\nbig data technologies (Spark, Airflow, BigQuery), cloud infrastructure,\nand applying NL", "source": "pattern_match"}, {"skill": "tensorflow", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "scikit-learn", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "plotly", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "mlflow", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "kubeflow", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "keras", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "seaborn", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "jup<PERSON><PERSON>", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "databases": [{"skill": "big<PERSON>y", "years": null, "confidence": 1.0, "context": "@gmail,com\nbig data technologies (Spark, Airflow, BigQuery), cloud infrastructure,\nand applying NLP and sent", "source": "pattern_match"}, {"skill": "redshift", "years": null, "confidence": 1.0, "context": "and saving significant cloud computing costs.\nAWS Redshift\nADIA - Lead Data Engineer 2018 - 2020\nHive Lead D", "source": "pattern_match"}, {"skill": "mysql", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "postgresql", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "sqlite", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "elasticsearch", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "oracle", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "influxdb", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "snowflake", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "clickhouse", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "redis", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "neo4j", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "couchbase", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "programming_languages": [{"skill": "python", "years": null, "confidence": 1.0, "context": "support critical business\nLanguages\nintelligence.\nPython Automated data migration from legacy systems to G", "source": "pattern_match"}, {"skill": "java", "years": null, "confidence": 1.0, "context": "using a CDC solution, ensuring zero downtime and\nJava\nimproving data accessibility for the analytics te", "source": "pattern_match"}, {"skill": "javascript", "years": null, "confidence": 1.0, "context": "roving data accessibility for the analytics team.\nJavascript\nPartnered with the Data Analytics team to build a", "source": "pattern_match"}, {"skill": "sql", "years": null, "confidence": 1.0, "context": "ith the Data Analytics team to build and optimize\nSQL mission-critical dashboards and reports for the B", "source": "pattern_match"}, {"skill": "r", "years": 10, "confidence": 0.9930092096328735, "context": "Terra", "source": "ner_entity"}, {"skill": "kotlin", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "scala", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "matlab", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "html", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "perl", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "lua", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "haskell", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "clojure", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "typescript", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "big_data": [{"skill": "hdfs", "years": null, "confidence": 1.0, "context": "hion Group 2020 - 2021\nSpark\nSenior Data Engineer\nHDFS\nMaintained and scaled data infrastructure, develo", "source": "pattern_match"}, {"skill": "hive", "years": null, "confidence": 1.0, "context": "WS Redshift\nADIA - Lead Data Engineer 2018 - 2020\nHive Lead Data Engineer\n<PERSON><PERSON>\nLed the design and d", "source": "pattern_match"}, {"skill": "apache spark", "years": null, "confidence": 0.9234862327575684, "context": "Spark", "source": "ner_entity"}, {"skill": "flink", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "hbase", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "zookeeper", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "parquet", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "devops_tools": [{"skill": "terraform", "years": null, "confidence": 1.0, "context": "Data Engineer 2018 - 2020\nHive Lead Data Engineer\nTerra<PERSON>\nLed the design and development of a text-mining p", "source": "pattern_match"}, {"skill": "docker", "years": null, "confidence": 1.0, "context": "ign and development of a text-mining platform for\nDocker trade surveillance, processing thousands of chat", "source": "pattern_match"}, {"skill": "ansible", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "gitlab ci", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "helm", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "elk stack", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "new relic", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "kubernetes", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "jenkins", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "prometheus", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "cloud_platforms": [{"skill": "aws", "years": null, "confidence": 1.0, "context": "a processing jobs, reducing execution time by 30%\nAWS EMR\nand saving significant cloud computing costs.", "source": "pattern_match"}, {"skill": "gcp", "years": null, "confidence": 1.0, "context": "plications.\nMigrate our data platform from OCI to GCP with limited downtime.\nSKILLS Engineered and laun", "source": "pattern_match"}, {"skill": "google cloud", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "alibaba cloud", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "digitalocean", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "vercel", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "netlify", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "cloudflare", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "azure", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "web_frameworks": [{"skill": "angular", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "svelte", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "flask", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "laravel", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "rails", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "blazor", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "react", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "vue", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "next.js", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "express", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "asp.net", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "ember.js", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "mobile_frameworks": [{"skill": "flutter", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "kotlin multiplatform", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "react native", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "phonegap", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "development_tools": [{"skill": "mercurial", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "confluence", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "slack", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "linux", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "powershell", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "intellij", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "eclipse", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "teams", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "vscode", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "swagger", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "sketch", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}], "testing_frameworks": [{"skill": "selenium", "years": null, "confidence": 0.789925754070282, "context": "L", "source": "ner_entity"}, {"skill": "jest", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "cypress", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "pytest", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "unittest", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "testng", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "cucumber", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}, {"skill": "jasmine", "years": null, "confidence": 0.7143715620040894, "context": "E", "source": "ner_entity"}]}, "additional_info": {"total_experience": null, "education": ["bachelor of software engineering", "ms and", "machine learning solutions from the ground up", "mail,com", "ms. seeking to leverage deep technical knowledge to drive data", "mara 2021 - now", "bachelor of software engineering", "marts) using open-source technologies,", "mated data migration from legacy systems to google", "making cycle", "bal fashion group 2020 - 2021", "maintained and scaled data infrastructure, developing new etl", "bs, reducing execution time by 30%", "mation from", "machine learning system for the automated approval", "manual moderation workload by 75%", "ms, which increased user engagement and drove a", "back, providing actionable insights into company culture and", "mance across key dimensions", "management, order", "ms.\nbuilt a distributed web crawling system to collect and process", "mation from competitor websites, supporting", "ma solution 2010 - 2011"], "certifications": ["aws emr", "aws redshift"], "projects": [], "contact_info": {}}, "summary": {"total_skills_found": 101, "skills_by_category": {"data_science_ml": 11, "databases": 13, "programming_languages": 14, "big_data": 7, "devops_tools": 11, "cloud_platforms": 10, "web_frameworks": 12, "mobile_frameworks": 4, "development_tools": 11, "testing_frameworks": 8}, "category_scores": {"data_science_ml": 0.8075153827667236, "databases": 0.8048092860441941, "programming_languages": 0.9299854295594352, "big_data": 0.8366466675485883, "devops_tools": 0.8075153827667236, "cloud_platforms": 0.8168297648429871, "web_frameworks": 0.7521486580371857, "mobile_frameworks": 0.7521486580371857, "development_tools": 0.7624515024098483, "testing_frameworks": 0.7238158360123634}, "top_skills": [{"skill": "r", "years": 10, "confidence": 0.9930092096328735, "context": "Terra", "source": "ner_entity"}, {"skill": "spark", "years": null, "confidence": 1.0, "context": "se in\nhoangds121@gmail,com\nbig data technologies (Spark, Airflow, BigQuery), cloud infrastructure,\nand ap", "source": "pattern_match"}, {"skill": "airflow", "years": null, "confidence": 1.0, "context": "oangds121@gmail,com\nbig data technologies (Spark, Airflow, BigQuery), cloud infrastructure,\nand applying NL", "source": "pattern_match"}, {"skill": "big<PERSON>y", "years": null, "confidence": 1.0, "context": "@gmail,com\nbig data technologies (Spark, Airflow, BigQuery), cloud infrastructure,\nand applying NLP and sent", "source": "pattern_match"}, {"skill": "redshift", "years": null, "confidence": 1.0, "context": "and saving significant cloud computing costs.\nAWS Redshift\nADIA - Lead Data Engineer 2018 - 2020\nHive Lead D", "source": "pattern_match"}, {"skill": "python", "years": null, "confidence": 1.0, "context": "support critical business\nLanguages\nintelligence.\nPython Automated data migration from legacy systems to G", "source": "pattern_match"}, {"skill": "java", "years": null, "confidence": 1.0, "context": "using a CDC solution, ensuring zero downtime and\nJava\nimproving data accessibility for the analytics te", "source": "pattern_match"}, {"skill": "javascript", "years": null, "confidence": 1.0, "context": "roving data accessibility for the analytics team.\nJavascript\nPartnered with the Data Analytics team to build a", "source": "pattern_match"}, {"skill": "sql", "years": null, "confidence": 1.0, "context": "ith the Data Analytics team to build and optimize\nSQL mission-critical dashboards and reports for the B", "source": "pattern_match"}, {"skill": "hdfs", "years": null, "confidence": 1.0, "context": "hion Group 2020 - 2021\nSpark\nSenior Data Engineer\nHDFS\nMaintained and scaled data infrastructure, develo", "source": "pattern_match"}], "total_experience_years": null, "education_count": 23, "certifications_count": 2, "has_contact_info": false, "strongest_category": "programming_languages"}, "personal_info": {"name": "HOANG PHAN", "email": null, "phone": null, "location": "ULMFiT,\nBE", "linkedin": null, "github": null, "portfolio": null, "summary": null, "objective": null}, "role_analysis": {"best_fit_role": "data_scientist", "role_rankings": [["data_scientist", 196.5], ["devops_engineer", 185.5], ["machine_learning_engineer", 182.0], ["backend_developer", 179.0], ["data_engineer", 172.0], ["full_stack_developer", 172.0], ["frontend_developer", 168.0], ["mobile_developer", 115.0]], "detailed_analysis": {"data_scientist": {"score": 196.5, "required_skills_match": ["python", "r", "sql", "scikit-learn"], "preferred_skills_match": ["tensorflow", "jup<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn"], "missing_required_skills": ["pandas", "numpy"], "missing_preferred_skills": ["pytorch"], "keyword_matches": ["machine learning"], "experience_level": "entry", "fit_percentage": 70.66666666666666, "strengths": [], "recommendations": ["Develop skills in: pandas, numpy", "Gain more experience (current: 1.2, required: 2)"], "category_scores": {"programming_languages": 35.0, "data_science_ml": 38.5, "databases": 19.5, "cloud_platforms": 10.0, "big_data": 10.5}}, "data_engineer": {"score": 172.0, "required_skills_match": ["python", "sql", "spark", "airflow"], "preferred_skills_match": ["aws", "docker", "kubernetes", "terraform"], "missing_required_skills": ["kafka"], "missing_preferred_skills": ["hadoop"], "keyword_matches": ["etl"], "experience_level": "entry", "fit_percentage": 80.0, "strengths": ["Strong foundation in data engineer core technologies"], "recommendations": ["Develop skills in: kafka", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"programming_languages": 28.000000000000004, "databases": 32.5, "big_data": 17.5, "cloud_platforms": 20.0, "devops_tools": 11.0}}, "full_stack_developer": {"score": 172.0, "required_skills_match": ["javascript", "html", "react"], "preferred_skills_match": ["typescript", "docker", "aws", "postgresql"], "missing_required_skills": ["css", "node.js"], "missing_preferred_skills": ["mongodb"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 66.0, "strengths": [], "recommendations": ["Develop skills in: css, node.js", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 35.0, "web_frameworks": 36.0, "databases": 19.5, "cloud_platforms": 15.0, "development_tools": 16.5}}, "backend_developer": {"score": 179.0, "required_skills_match": ["java", "python", "sql"], "preferred_skills_match": ["docker", "kubernetes", "aws", "redis", "postgresql"], "missing_required_skills": ["spring", "api"], "missing_preferred_skills": [], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 72.0, "strengths": [], "recommendations": ["Develop skills in: spring, api", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 42.0, "web_frameworks": 30.0, "databases": 26.0, "cloud_platforms": 15.0, "devops_tools": 11.0}}, "frontend_developer": {"score": 168.0, "required_skills_match": ["javascript", "html", "react"], "preferred_skills_match": ["typescript", "vue", "angular"], "missing_required_skills": ["css"], "missing_preferred_skills": ["webpack", "sass"], "keyword_matches": ["ui", "component"], "experience_level": "entry", "fit_percentage": 70.5, "strengths": [], "recommendations": ["Develop skills in: css", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 35.0, "web_frameworks": 48.00000000000001, "development_tools": 22.0, "testing_frameworks": 12.0}}, "devops_engineer": {"score": 185.5, "required_skills_match": ["docker", "kubernetes", "aws", "terraform", "jenkins"], "preferred_skills_match": ["ansible", "prometheus", "helm"], "missing_required_skills": [], "missing_preferred_skills": ["grafana", "istio"], "keyword_matches": ["infrastructure", "monitoring"], "experience_level": "entry", "fit_percentage": 88.0, "strengths": ["Strong foundation in devops engineer core technologies"], "recommendations": ["Gain more experience (current: 0.0, required: 2)"], "category_scores": {"devops_tools": 44.0, "cloud_platforms": 30.0, "programming_languages": 21.0, "databases": 19.5}}, "mobile_developer": {"score": 115.0, "required_skills_match": ["kotlin", "react native"], "preferred_skills_match": ["flutter"], "missing_required_skills": ["swift"], "missing_preferred_skills": ["ios", "android", "firebase", "xcode"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 52.66666666666666, "strengths": [], "recommendations": ["Develop skills in: swift", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 42.0, "mobile_frameworks": 16.0, "development_tools": 22.0, "cloud_platforms": 10.0}}, "machine_learning_engineer": {"score": 182.0, "required_skills_match": ["python", "tensorflow", "docker", "kubernetes"], "preferred_skills_match": ["mlflow", "kubeflow", "aws", "spark", "airflow"], "missing_required_skills": ["pytorch"], "missing_preferred_skills": [], "keyword_matches": ["ai"], "experience_level": "entry", "fit_percentage": 86.0, "strengths": ["Strong foundation in machine learning engineer core technologies"], "recommendations": ["Develop skills in: pytorch", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"programming_languages": 28.000000000000004, "data_science_ml": 44.0, "cloud_platforms": 20.0, "devops_tools": 22.0}}}, "top_3_roles": [["data_scientist", 196.5], ["devops_engineer", 185.5], ["machine_learning_engineer", 182.0]]}, "skill_combinations": {"technology_stacks": [{"stack": "ML Engineering Stack", "completeness": 100.0, "matched_skills": ["python", "tensorflow", "docker", "kubernetes", "aws"], "missing_skills": [], "experience_level": "novice"}, {"stack": "DevOps Stack", "completeness": 100.0, "matched_skills": ["docker", "kubernetes", "jenkins", "terraform", "aws"], "missing_skills": [], "experience_level": "novice"}, {"stack": "<PERSON><PERSON><PERSON>", "completeness": 75.0, "matched_skills": ["python", "postgresql", "redis"], "missing_skills": ["django"], "experience_level": "novice"}, {"stack": "Data Science Stack", "completeness": 60.0, "matched_skills": ["python", "scikit-learn", "jup<PERSON><PERSON>"], "missing_skills": ["pandas", "numpy"], "experience_level": "novice"}, {"stack": "MEAN", "completeness": 50.0, "matched_skills": ["express", "angular"], "missing_skills": ["mongodb", "node.js"], "experience_level": "novice"}, {"stack": "MERN", "completeness": 50.0, "matched_skills": ["express", "react"], "missing_skills": ["mongodb", "node.js"], "experience_level": "novice"}, {"stack": "LAMP", "completeness": 50.0, "matched_skills": ["linux", "mysql"], "missing_skills": ["apache", "php"], "experience_level": "novice"}, {"stack": "Spring Stack", "completeness": 50.0, "matched_skills": ["java", "mysql"], "missing_skills": ["spring", "maven"], "experience_level": "novice"}, {"stack": "React Native Stack", "completeness": 50.0, "matched_skills": ["react native", "javascript"], "missing_skills": ["redux", "firebase"], "experience_level": "novice"}, {"stack": "Flutter Stack", "completeness": 25.0, "matched_skills": ["flutter"], "missing_skills": ["dart", "firebase", "android sdk"], "experience_level": "novice"}], "skill_clusters": {"programming_languages": {"skills": [{"skill": "python", "years": null, "confidence": 1.0}, {"skill": "java", "years": null, "confidence": 1.0}, {"skill": "javascript", "years": null, "confidence": 1.0}, {"skill": "typescript", "years": null, "confidence": 0.7143715620040894}, {"skill": "kotlin", "years": null, "confidence": 0.789925754070282}, {"skill": "scala", "years": null, "confidence": 0.789925754070282}, {"skill": "r", "years": 10, "confidence": 0.9930092096328735}, {"skill": "matlab", "years": null, "confidence": 0.789925754070282}, {"skill": "sql", "years": null, "confidence": 1.0}, {"skill": "html", "years": null, "confidence": 0.789925754070282}, {"skill": "perl", "years": null, "confidence": 0.789925754070282}, {"skill": "lua", "years": null, "confidence": 0.789925754070282}, {"skill": "haskell", "years": null, "confidence": 0.789925754070282}, {"skill": "clojure", "years": null, "confidence": 0.789925754070282}], "count": 14, "avg_experience": 0.7142857142857143, "strength_level": "moderate"}, "web_frameworks": {"skills": [{"skill": "react", "years": null, "confidence": 0.7143715620040894}, {"skill": "angular", "years": null, "confidence": 0.789925754070282}, {"skill": "vue", "years": null, "confidence": 0.7143715620040894}, {"skill": "svelte", "years": null, "confidence": 0.789925754070282}, {"skill": "next.js", "years": null, "confidence": 0.7143715620040894}, {"skill": "flask", "years": null, "confidence": 0.789925754070282}, {"skill": "express", "years": null, "confidence": 0.7143715620040894}, {"skill": "laravel", "years": null, "confidence": 0.789925754070282}, {"skill": "rails", "years": null, "confidence": 0.789925754070282}, {"skill": "asp.net", "years": null, "confidence": 0.7143715620040894}, {"skill": "blazor", "years": null, "confidence": 0.789925754070282}, {"skill": "ember.js", "years": null, "confidence": 0.7143715620040894}], "count": 12, "avg_experience": 0.0, "strength_level": "moderate"}, "mobile_frameworks": {"skills": [{"skill": "react native", "years": null, "confidence": 0.7143715620040894}, {"skill": "flutter", "years": null, "confidence": 0.789925754070282}, {"skill": "phonegap", "years": null, "confidence": 0.7143715620040894}, {"skill": "kotlin multiplatform", "years": null, "confidence": 0.789925754070282}], "count": 4, "avg_experience": 0.0, "strength_level": "basic"}, "data_science_ml": {"skills": [{"skill": "tensorflow", "years": null, "confidence": 0.789925754070282}, {"skill": "keras", "years": null, "confidence": 0.7143715620040894}, {"skill": "scikit-learn", "years": null, "confidence": 0.789925754070282}, {"skill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "years": null, "confidence": 0.789925754070282}, {"skill": "seaborn", "years": null, "confidence": 0.7143715620040894}, {"skill": "plotly", "years": null, "confidence": 0.789925754070282}, {"skill": "jup<PERSON><PERSON>", "years": null, "confidence": 0.7143715620040894}, {"skill": "spark", "years": null, "confidence": 1.0}, {"skill": "airflow", "years": null, "confidence": 1.0}, {"skill": "mlflow", "years": null, "confidence": 0.789925754070282}, {"skill": "kubeflow", "years": null, "confidence": 0.789925754070282}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "databases": {"skills": [{"skill": "mysql", "years": null, "confidence": 0.789925754070282}, {"skill": "postgresql", "years": null, "confidence": 0.789925754070282}, {"skill": "redis", "years": null, "confidence": 0.7143715620040894}, {"skill": "elasticsearch", "years": null, "confidence": 0.789925754070282}, {"skill": "oracle", "years": null, "confidence": 0.789925754070282}, {"skill": "sqlite", "years": null, "confidence": 0.789925754070282}, {"skill": "neo4j", "years": null, "confidence": 0.7143715620040894}, {"skill": "influxdb", "years": null, "confidence": 0.789925754070282}, {"skill": "snowflake", "years": null, "confidence": 0.789925754070282}, {"skill": "big<PERSON>y", "years": null, "confidence": 1.0}, {"skill": "redshift", "years": null, "confidence": 1.0}, {"skill": "clickhouse", "years": null, "confidence": 0.789925754070282}, {"skill": "couchbase", "years": null, "confidence": 0.7143715620040894}], "count": 13, "avg_experience": 0.0, "strength_level": "moderate"}, "cloud_platforms": {"skills": [{"skill": "aws", "years": null, "confidence": 1.0}, {"skill": "azure", "years": null, "confidence": 0.7143715620040894}, {"skill": "gcp", "years": null, "confidence": 1.0}, {"skill": "google cloud", "years": null, "confidence": 0.789925754070282}, {"skill": "alibaba cloud", "years": null, "confidence": 0.789925754070282}, {"skill": "digitalocean", "years": null, "confidence": 0.789925754070282}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.7143715620040894}, {"skill": "vercel", "years": null, "confidence": 0.789925754070282}, {"skill": "netlify", "years": null, "confidence": 0.789925754070282}, {"skill": "cloudflare", "years": null, "confidence": 0.789925754070282}], "count": 10, "avg_experience": 0.0, "strength_level": "moderate"}, "devops_tools": {"skills": [{"skill": "docker", "years": null, "confidence": 1.0}, {"skill": "kubernetes", "years": null, "confidence": 0.7143715620040894}, {"skill": "terraform", "years": null, "confidence": 1.0}, {"skill": "ansible", "years": null, "confidence": 0.789925754070282}, {"skill": "jenkins", "years": null, "confidence": 0.7143715620040894}, {"skill": "gitlab ci", "years": null, "confidence": 0.789925754070282}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.789925754070282}, {"skill": "helm", "years": null, "confidence": 0.789925754070282}, {"skill": "prometheus", "years": null, "confidence": 0.7143715620040894}, {"skill": "elk stack", "years": null, "confidence": 0.789925754070282}, {"skill": "new relic", "years": null, "confidence": 0.789925754070282}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "development_tools": {"skills": [{"skill": "mercurial", "years": null, "confidence": 0.789925754070282}, {"skill": "confluence", "years": null, "confidence": 0.789925754070282}, {"skill": "slack", "years": null, "confidence": 0.789925754070282}, {"skill": "teams", "years": null, "confidence": 0.7143715620040894}, {"skill": "linux", "years": null, "confidence": 0.789925754070282}, {"skill": "powershell", "years": null, "confidence": 0.789925754070282}, {"skill": "vscode", "years": null, "confidence": 0.7143715620040894}, {"skill": "intellij", "years": null, "confidence": 0.789925754070282}, {"skill": "eclipse", "years": null, "confidence": 0.789925754070282}, {"skill": "swagger", "years": null, "confidence": 0.7143715620040894}, {"skill": "sketch", "years": null, "confidence": 0.7143715620040894}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "testing_frameworks": {"skills": [{"skill": "jest", "years": null, "confidence": 0.7143715620040894}, {"skill": "cypress", "years": null, "confidence": 0.7143715620040894}, {"skill": "selenium", "years": null, "confidence": 0.789925754070282}, {"skill": "pytest", "years": null, "confidence": 0.7143715620040894}, {"skill": "unittest", "years": null, "confidence": 0.7143715620040894}, {"skill": "testng", "years": null, "confidence": 0.7143715620040894}, {"skill": "cucumber", "years": null, "confidence": 0.7143715620040894}, {"skill": "jasmine", "years": null, "confidence": 0.7143715620040894}], "count": 8, "avg_experience": 0.0, "strength_level": "basic"}, "big_data": {"skills": [{"skill": "apache spark", "years": null, "confidence": 0.9234862327575684}, {"skill": "flink", "years": null, "confidence": 0.789925754070282}, {"skill": "hive", "years": null, "confidence": 1.0}, {"skill": "hbase", "years": null, "confidence": 0.7143715620040894}, {"skill": "zookeeper", "years": null, "confidence": 0.7143715620040894}, {"skill": "hdfs", "years": null, "confidence": 1.0}, {"skill": "parquet", "years": null, "confidence": 0.7143715620040894}], "count": 7, "avg_experience": 0.0, "strength_level": "basic"}}, "complementary_skills": {}, "skill_gaps": {}, "stack_completeness": {}}, "comprehensive_report": {"candidate_profile": {"personal_info": {"name": "HOANG PHAN", "email": null, "phone": null, "location": "ULMFiT,\nBE", "linkedin": null, "github": null, "portfolio": null, "summary": null, "objective": null}, "professional_summary": "HOANG PHAN appears to be best suited for a Data Scientist role. Strong background in programming languages with 14 identified skills. Key expertise includes r, spark, airflow. ", "contact_completeness": {"required_complete": 1, "required_total": 3, "optional_complete": 1, "optional_total": 4, "missing_required": ["email", "phone"], "missing_optional": ["linkedin", "github", "portfolio"], "required_percentage": 33.33333333333333, "optional_percentage": 25.0, "overall_score": 30.83333333333333}}, "role_recommendations": {"best_fit": "data_scientist", "top_matches": [["data_scientist", 196.5], ["devops_engineer", 185.5], ["machine_learning_engineer", 182.0]], "detailed_fit_analysis": {"data_scientist": {"score": 196.5, "required_skills_match": ["python", "r", "sql", "scikit-learn"], "preferred_skills_match": ["tensorflow", "jup<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn"], "missing_required_skills": ["pandas", "numpy"], "missing_preferred_skills": ["pytorch"], "keyword_matches": ["machine learning"], "experience_level": "entry", "fit_percentage": 70.66666666666666, "strengths": [], "recommendations": ["Develop skills in: pandas, numpy", "Gain more experience (current: 1.2, required: 2)"], "category_scores": {"programming_languages": 35.0, "data_science_ml": 38.5, "databases": 19.5, "cloud_platforms": 10.0, "big_data": 10.5}}, "data_engineer": {"score": 172.0, "required_skills_match": ["python", "sql", "spark", "airflow"], "preferred_skills_match": ["aws", "docker", "kubernetes", "terraform"], "missing_required_skills": ["kafka"], "missing_preferred_skills": ["hadoop"], "keyword_matches": ["etl"], "experience_level": "entry", "fit_percentage": 80.0, "strengths": ["Strong foundation in data engineer core technologies"], "recommendations": ["Develop skills in: kafka", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"programming_languages": 28.000000000000004, "databases": 32.5, "big_data": 17.5, "cloud_platforms": 20.0, "devops_tools": 11.0}}, "full_stack_developer": {"score": 172.0, "required_skills_match": ["javascript", "html", "react"], "preferred_skills_match": ["typescript", "docker", "aws", "postgresql"], "missing_required_skills": ["css", "node.js"], "missing_preferred_skills": ["mongodb"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 66.0, "strengths": [], "recommendations": ["Develop skills in: css, node.js", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 35.0, "web_frameworks": 36.0, "databases": 19.5, "cloud_platforms": 15.0, "development_tools": 16.5}}, "backend_developer": {"score": 179.0, "required_skills_match": ["java", "python", "sql"], "preferred_skills_match": ["docker", "kubernetes", "aws", "redis", "postgresql"], "missing_required_skills": ["spring", "api"], "missing_preferred_skills": [], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 72.0, "strengths": [], "recommendations": ["Develop skills in: spring, api", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 42.0, "web_frameworks": 30.0, "databases": 26.0, "cloud_platforms": 15.0, "devops_tools": 11.0}}, "frontend_developer": {"score": 168.0, "required_skills_match": ["javascript", "html", "react"], "preferred_skills_match": ["typescript", "vue", "angular"], "missing_required_skills": ["css"], "missing_preferred_skills": ["webpack", "sass"], "keyword_matches": ["ui", "component"], "experience_level": "entry", "fit_percentage": 70.5, "strengths": [], "recommendations": ["Develop skills in: css", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 35.0, "web_frameworks": 48.00000000000001, "development_tools": 22.0, "testing_frameworks": 12.0}}, "devops_engineer": {"score": 185.5, "required_skills_match": ["docker", "kubernetes", "aws", "terraform", "jenkins"], "preferred_skills_match": ["ansible", "prometheus", "helm"], "missing_required_skills": [], "missing_preferred_skills": ["grafana", "istio"], "keyword_matches": ["infrastructure", "monitoring"], "experience_level": "entry", "fit_percentage": 88.0, "strengths": ["Strong foundation in devops engineer core technologies"], "recommendations": ["Gain more experience (current: 0.0, required: 2)"], "category_scores": {"devops_tools": 44.0, "cloud_platforms": 30.0, "programming_languages": 21.0, "databases": 19.5}}, "mobile_developer": {"score": 115.0, "required_skills_match": ["kotlin", "react native"], "preferred_skills_match": ["flutter"], "missing_required_skills": ["swift"], "missing_preferred_skills": ["ios", "android", "firebase", "xcode"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 52.66666666666666, "strengths": [], "recommendations": ["Develop skills in: swift", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 42.0, "mobile_frameworks": 16.0, "development_tools": 22.0, "cloud_platforms": 10.0}}, "machine_learning_engineer": {"score": 182.0, "required_skills_match": ["python", "tensorflow", "docker", "kubernetes"], "preferred_skills_match": ["mlflow", "kubeflow", "aws", "spark", "airflow"], "missing_required_skills": ["pytorch"], "missing_preferred_skills": [], "keyword_matches": ["ai"], "experience_level": "entry", "fit_percentage": 86.0, "strengths": ["Strong foundation in machine learning engineer core technologies"], "recommendations": ["Develop skills in: pytorch", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"programming_languages": 28.000000000000004, "data_science_ml": 44.0, "cloud_platforms": 20.0, "devops_tools": 22.0}}}, "career_progression": {"current_level": "entry", "next_roles": ["Mid-level data scientist", "Senior data scientist (2-3 years)", "Technical Lead (4-5 years)"], "progression_timeline": {"1-2 years": "Focus on deepening data scientist skills", "2-4 years": "<PERSON>ain experience in system design and mentoring", "4+ years": "Consider technical leadership or specialization"}, "skill_requirements": {"technical": ["system design basics", "testing frameworks", "ci/cd"], "soft_skills": ["communication", "collaboration", "problem solving"]}, "leadership_readiness": true}}, "technical_assessment": {"skill_summary": {"total_skills": 101, "skills_with_experience": 1, "average_experience": 10.0, "experience_distribution": {"expert": 1, "advanced": 0, "intermediate": 0, "beginner": 0, "novice": 100}, "most_experienced_skill": "r"}, "technology_stacks": [{"stack": "ML Engineering Stack", "completeness": 100.0, "matched_skills": ["python", "tensorflow", "docker", "kubernetes", "aws"], "missing_skills": [], "experience_level": "novice"}, {"stack": "DevOps Stack", "completeness": 100.0, "matched_skills": ["docker", "kubernetes", "jenkins", "terraform", "aws"], "missing_skills": [], "experience_level": "novice"}, {"stack": "<PERSON><PERSON><PERSON>", "completeness": 75.0, "matched_skills": ["python", "postgresql", "redis"], "missing_skills": ["django"], "experience_level": "novice"}, {"stack": "Data Science Stack", "completeness": 60.0, "matched_skills": ["python", "scikit-learn", "jup<PERSON><PERSON>"], "missing_skills": ["pandas", "numpy"], "experience_level": "novice"}, {"stack": "MEAN", "completeness": 50.0, "matched_skills": ["express", "angular"], "missing_skills": ["mongodb", "node.js"], "experience_level": "novice"}, {"stack": "MERN", "completeness": 50.0, "matched_skills": ["express", "react"], "missing_skills": ["mongodb", "node.js"], "experience_level": "novice"}, {"stack": "LAMP", "completeness": 50.0, "matched_skills": ["linux", "mysql"], "missing_skills": ["apache", "php"], "experience_level": "novice"}, {"stack": "Spring Stack", "completeness": 50.0, "matched_skills": ["java", "mysql"], "missing_skills": ["spring", "maven"], "experience_level": "novice"}, {"stack": "React Native Stack", "completeness": 50.0, "matched_skills": ["react native", "javascript"], "missing_skills": ["redux", "firebase"], "experience_level": "novice"}, {"stack": "Flutter Stack", "completeness": 25.0, "matched_skills": ["flutter"], "missing_skills": ["dart", "firebase", "android sdk"], "experience_level": "novice"}], "skill_clusters": {"programming_languages": {"skills": [{"skill": "python", "years": null, "confidence": 1.0}, {"skill": "java", "years": null, "confidence": 1.0}, {"skill": "javascript", "years": null, "confidence": 1.0}, {"skill": "typescript", "years": null, "confidence": 0.7143715620040894}, {"skill": "kotlin", "years": null, "confidence": 0.789925754070282}, {"skill": "scala", "years": null, "confidence": 0.789925754070282}, {"skill": "r", "years": 10, "confidence": 0.9930092096328735}, {"skill": "matlab", "years": null, "confidence": 0.789925754070282}, {"skill": "sql", "years": null, "confidence": 1.0}, {"skill": "html", "years": null, "confidence": 0.789925754070282}, {"skill": "perl", "years": null, "confidence": 0.789925754070282}, {"skill": "lua", "years": null, "confidence": 0.789925754070282}, {"skill": "haskell", "years": null, "confidence": 0.789925754070282}, {"skill": "clojure", "years": null, "confidence": 0.789925754070282}], "count": 14, "avg_experience": 0.7142857142857143, "strength_level": "moderate"}, "web_frameworks": {"skills": [{"skill": "react", "years": null, "confidence": 0.7143715620040894}, {"skill": "angular", "years": null, "confidence": 0.789925754070282}, {"skill": "vue", "years": null, "confidence": 0.7143715620040894}, {"skill": "svelte", "years": null, "confidence": 0.789925754070282}, {"skill": "next.js", "years": null, "confidence": 0.7143715620040894}, {"skill": "flask", "years": null, "confidence": 0.789925754070282}, {"skill": "express", "years": null, "confidence": 0.7143715620040894}, {"skill": "laravel", "years": null, "confidence": 0.789925754070282}, {"skill": "rails", "years": null, "confidence": 0.789925754070282}, {"skill": "asp.net", "years": null, "confidence": 0.7143715620040894}, {"skill": "blazor", "years": null, "confidence": 0.789925754070282}, {"skill": "ember.js", "years": null, "confidence": 0.7143715620040894}], "count": 12, "avg_experience": 0.0, "strength_level": "moderate"}, "mobile_frameworks": {"skills": [{"skill": "react native", "years": null, "confidence": 0.7143715620040894}, {"skill": "flutter", "years": null, "confidence": 0.789925754070282}, {"skill": "phonegap", "years": null, "confidence": 0.7143715620040894}, {"skill": "kotlin multiplatform", "years": null, "confidence": 0.789925754070282}], "count": 4, "avg_experience": 0.0, "strength_level": "basic"}, "data_science_ml": {"skills": [{"skill": "tensorflow", "years": null, "confidence": 0.789925754070282}, {"skill": "keras", "years": null, "confidence": 0.7143715620040894}, {"skill": "scikit-learn", "years": null, "confidence": 0.789925754070282}, {"skill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "years": null, "confidence": 0.789925754070282}, {"skill": "seaborn", "years": null, "confidence": 0.7143715620040894}, {"skill": "plotly", "years": null, "confidence": 0.789925754070282}, {"skill": "jup<PERSON><PERSON>", "years": null, "confidence": 0.7143715620040894}, {"skill": "spark", "years": null, "confidence": 1.0}, {"skill": "airflow", "years": null, "confidence": 1.0}, {"skill": "mlflow", "years": null, "confidence": 0.789925754070282}, {"skill": "kubeflow", "years": null, "confidence": 0.789925754070282}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "databases": {"skills": [{"skill": "mysql", "years": null, "confidence": 0.789925754070282}, {"skill": "postgresql", "years": null, "confidence": 0.789925754070282}, {"skill": "redis", "years": null, "confidence": 0.7143715620040894}, {"skill": "elasticsearch", "years": null, "confidence": 0.789925754070282}, {"skill": "oracle", "years": null, "confidence": 0.789925754070282}, {"skill": "sqlite", "years": null, "confidence": 0.789925754070282}, {"skill": "neo4j", "years": null, "confidence": 0.7143715620040894}, {"skill": "influxdb", "years": null, "confidence": 0.789925754070282}, {"skill": "snowflake", "years": null, "confidence": 0.789925754070282}, {"skill": "big<PERSON>y", "years": null, "confidence": 1.0}, {"skill": "redshift", "years": null, "confidence": 1.0}, {"skill": "clickhouse", "years": null, "confidence": 0.789925754070282}, {"skill": "couchbase", "years": null, "confidence": 0.7143715620040894}], "count": 13, "avg_experience": 0.0, "strength_level": "moderate"}, "cloud_platforms": {"skills": [{"skill": "aws", "years": null, "confidence": 1.0}, {"skill": "azure", "years": null, "confidence": 0.7143715620040894}, {"skill": "gcp", "years": null, "confidence": 1.0}, {"skill": "google cloud", "years": null, "confidence": 0.789925754070282}, {"skill": "alibaba cloud", "years": null, "confidence": 0.789925754070282}, {"skill": "digitalocean", "years": null, "confidence": 0.789925754070282}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.7143715620040894}, {"skill": "vercel", "years": null, "confidence": 0.789925754070282}, {"skill": "netlify", "years": null, "confidence": 0.789925754070282}, {"skill": "cloudflare", "years": null, "confidence": 0.789925754070282}], "count": 10, "avg_experience": 0.0, "strength_level": "moderate"}, "devops_tools": {"skills": [{"skill": "docker", "years": null, "confidence": 1.0}, {"skill": "kubernetes", "years": null, "confidence": 0.7143715620040894}, {"skill": "terraform", "years": null, "confidence": 1.0}, {"skill": "ansible", "years": null, "confidence": 0.789925754070282}, {"skill": "jenkins", "years": null, "confidence": 0.7143715620040894}, {"skill": "gitlab ci", "years": null, "confidence": 0.789925754070282}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.789925754070282}, {"skill": "helm", "years": null, "confidence": 0.789925754070282}, {"skill": "prometheus", "years": null, "confidence": 0.7143715620040894}, {"skill": "elk stack", "years": null, "confidence": 0.789925754070282}, {"skill": "new relic", "years": null, "confidence": 0.789925754070282}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "development_tools": {"skills": [{"skill": "mercurial", "years": null, "confidence": 0.789925754070282}, {"skill": "confluence", "years": null, "confidence": 0.789925754070282}, {"skill": "slack", "years": null, "confidence": 0.789925754070282}, {"skill": "teams", "years": null, "confidence": 0.7143715620040894}, {"skill": "linux", "years": null, "confidence": 0.789925754070282}, {"skill": "powershell", "years": null, "confidence": 0.789925754070282}, {"skill": "vscode", "years": null, "confidence": 0.7143715620040894}, {"skill": "intellij", "years": null, "confidence": 0.789925754070282}, {"skill": "eclipse", "years": null, "confidence": 0.789925754070282}, {"skill": "swagger", "years": null, "confidence": 0.7143715620040894}, {"skill": "sketch", "years": null, "confidence": 0.7143715620040894}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "testing_frameworks": {"skills": [{"skill": "jest", "years": null, "confidence": 0.7143715620040894}, {"skill": "cypress", "years": null, "confidence": 0.7143715620040894}, {"skill": "selenium", "years": null, "confidence": 0.789925754070282}, {"skill": "pytest", "years": null, "confidence": 0.7143715620040894}, {"skill": "unittest", "years": null, "confidence": 0.7143715620040894}, {"skill": "testng", "years": null, "confidence": 0.7143715620040894}, {"skill": "cucumber", "years": null, "confidence": 0.7143715620040894}, {"skill": "jasmine", "years": null, "confidence": 0.7143715620040894}], "count": 8, "avg_experience": 0.0, "strength_level": "basic"}, "big_data": {"skills": [{"skill": "apache spark", "years": null, "confidence": 0.9234862327575684}, {"skill": "flink", "years": null, "confidence": 0.789925754070282}, {"skill": "hive", "years": null, "confidence": 1.0}, {"skill": "hbase", "years": null, "confidence": 0.7143715620040894}, {"skill": "zookeeper", "years": null, "confidence": 0.7143715620040894}, {"skill": "hdfs", "years": null, "confidence": 1.0}, {"skill": "parquet", "years": null, "confidence": 0.7143715620040894}], "count": 7, "avg_experience": 0.0, "strength_level": "basic"}}, "technical_strengths": ["Complete mastery of ML Engineering Stack technology stack", "Expert-level experience in r"], "skill_gaps": {"data_scientist": {"critical_gaps": ["pandas", "numpy"], "enhancement_opportunities": ["pytorch"]}, "data_engineer": {"critical_gaps": ["kafka"], "enhancement_opportunities": ["hadoop"]}, "full_stack_developer": {"critical_gaps": ["css", "node.js"], "enhancement_opportunities": ["mongodb"]}}}, "experience_analysis": {"total_experience": null, "experience_distribution": {"mean_years": 0.09900990099009901, "median_years": 0.0, "std_deviation": 0.9900990099009902, "max_years": 10, "min_years": 0, "distribution_type": "balanced"}, "seniority_indicators": {"leadership_keywords": 0, "architecture_keywords": 0, "mentoring_keywords": 0, "project_management_keywords": 0, "senior_level_skills": 2, "overall_level": "junior", "seniority_score": 0.6297029702970297}, "learning_trajectory": "continuous_learner"}, "recommendations": {"immediate_actions": ["Priority: Learn pandas to meet core requirements for data scientist", "Create a GitHub portfolio showcasing your technical skills"], "skill_development": {"short_term": ["pandas", "numpy"], "medium_term": ["pytorch"], "long_term": ["microservices", "system design", "machine learning"]}, "career_advice": ["Focus on gaining hands-on experience in data scientist through internships, projects, or entry-level positions", "Your skills align with multiple roles: consider data scientist, devops engineer"]}, "overall_assessment": {"marketability_score": {"overall_score": 76.19999999999999, "components": {"skill_diversity": 100, "experience_depth": 50.0, "role_fit": 70.66666666666666, "modern_skills": 100}, "grade": "B+", "market_readiness": "medium"}, "unique_value_proposition": "Full-stack expertise in ML Engineering Stack with 100% stack completeness", "competitive_advantages": ["Proficiency in high-demand technologies: kubernetes, aws, react", "Versatility to work in multiple roles: data scientist and devops engineer", "Broad technical knowledge across 101 different technologies"]}}}