{"file_path": "Hoang Phan CV.pdf", "timestamp": "2025-06-23T21:21:17.266443", "success": true, "error": null, "extraction_method": "pdfplumber", "raw_text": "HOANG PHAN CONTACT PROFILE Results-driven and innovative Lead Data Engineer with over 10 years +************** of experience designing, building, and optimizing data platforms and machine learning solutions from the ground up.\nProven expertise in hoangds121@gmail, com big data technologies (Spark, Airflow, Big Query), cloud infrastructure, and applying NLP and sentiment analysis to solve complex business District 7, Ho Chi Minh City problems.\nSeeking to leverage deep technical knowledge to drive data innovation in a challenging senior engineering role.\nEDUCATION WORK EXPERIENCE Tamara 2021 - NOW Bachelor of Software Engineering Lead Data Engineer University of Science (HCMUS) Build an AI platform that supports building LLM applications.\nMigrate our data platform from OCI to GCP with limited downtime.\nSKILLS Engineered and launched a comprehensive data platform from scratch (Data Lake, Data Marts) using open-source technologies, processing over 1TB of data daily to support critical business Languages intelligence.\nPython Automated data migration from legacy systems to Google Big Query using a CDC solution, ensuring zero downtime and Java improving data accessibility for the analytics team.\nJavascript Partnered with the Data Analytics team to build and optimize SQL mission-critical dashboards and reports for the Board of Directors, leading to a 20% faster decision-making cycle.\nBig Data & Cloud Global Fashion Group 2020 - 2021 Spark Senior Data Engineer HDFS Maintained and scaled data infrastructure, developing new ETL Airflow pipelines with Airflow and EMR to ingest data from multiple global Google Big Query regions into a centralized data lake.\nOptimized data processing jobs, reducing execution time by 30% AWS EMR and saving significant cloud computing costs.\nAWS Redshift ADIA - Lead Data Engineer 2018 - 2020 Hive Lead Data Engineer Terraform Led the design and development of a text-mining platform for Docker trade surveillance, processing thousands of chat logs daily to detect compliance faults and fraudulent activity.\nExtracted and structured critical transaction information from unstructured chat data, improving the efficiency of the compliance team by 40%.\nZalora LANGUAGES Senior Data Engineer 2016 - 2018 Data Engineer 2015 - 2016 Vietnamese Developed and integrated a personalized search solution using English (Fluent) Learning to Rank (LTR) models into SOLR, increasing search conversion rates by 15%.\nEngineered a machine learning system for the automated approval of customer reviews using state-of-the-art NLP models (ULMFi T, BERT), reducing manual moderation workload by 75%.\nBuilt and deployed an item-item recommendation engine for email and web platforms, which increased user engagement and drove a 10% uplift in cross-sell revenue.\nDesigned and constructed a scalable Data Science Platform using Mesos, Spark, and HDFS, empowering the data science team to experiment and deploy models more efficiently.\nTiny Pulse 2014 - 2015 Data Engineer Developed a sentiment analysis engine to process employee feedback, providing actionable insights into company culture and performance across key dimensions.\nCreated custom, data-driven reports for enterprise customers, enhancing client satisfaction and retention.\nAtlassian 2014 - 2014 Senior Developer Operated in a hybrid growth developer and data analyst role, designing and implementing A/B tests to validate new product hypotheses and evaluate feature impact.\nQueried and analyzed large datasets from Redshift and Hive to uncover user behavior patterns, presenting findings through visualizations to inform product strategy.\nMobivi 2011 - 2014 Senior Developer Architected and developed key components of the MCA E- Commerce platform, including the inventory management, order orchestration, and fulfillment systems.\nBuilt a distributed web crawling system to collect and process online product information from competitor websites, supporting competitive pricing strategies.\nTMA Solution 2010 - 2011 Junior Developer Contributed to the development of the NECA project, a network monitoring solution for fiber optic cable infrastructure.", "entities": [{"entity_group": "MISC", "score": 0.7709481120109558, "word": "Air", "start": 298, "end": 301}, {"entity_group": "LOC", "score": 0.9989216327667236, "word": "Ho Chi Minh City", "start": 419, "end": 435}, {"entity_group": "ORG", "score": 0.767189621925354, "word": "Software Engineering", "start": 612, "end": 632}, {"entity_group": "ORG", "score": 0.8729043006896973, "word": "Data Engineer University of Science", "start": 638, "end": 673}, {"entity_group": "ORG", "score": 0.836132824420929, "word": "HCMUS", "start": 675, "end": 680}, {"entity_group": "ORG", "score": 0.7008316516876221, "word": "OCI", "start": 774, "end": 777}, {"entity_group": "ORG", "score": 0.702987551689148, "word": "SKIL", "start": 807, "end": 811}, {"entity_group": "MISC", "score": 0.9738804697990417, "word": "Python", "start": 1023, "end": 1029}, {"entity_group": "MISC", "score": 0.7825215458869934, "word": "Google Big", "start": 1078, "end": 1088}, {"entity_group": "ORG", "score": 0.958082914352417, "word": "CDC", "start": 1103, "end": 1106}, {"entity_group": "MISC", "score": 0.9861903190612793, "word": "Java", "start": 1144, "end": 1148}, {"entity_group": "MISC", "score": 0.9845815300941467, "word": "Java", "start": 1201, "end": 1205}, {"entity_group": "ORG", "score": 0.9871965050697327, "word": "Data Analytics", "start": 1231, "end": 1245}, {"entity_group": "MISC", "score": 0.7520924806594849, "word": "SQL", "start": 1273, "end": 1276}, {"entity_group": "ORG", "score": 0.9641854166984558, "word": "Data & Cloud Global Fashion Group", "start": 1395, "end": 1428}, {"entity_group": "ORG", "score": 0.7250173687934875, "word": "##rk", "start": 1444, "end": 1446}, {"entity_group": "ORG", "score": 0.9803582429885864, "word": "HDFS", "start": 1468, "end": 1472}, {"entity_group": "MISC", "score": 0.8203909397125244, "word": "Google Big Que", "start": 1610, "end": 1624}, {"entity_group": "ORG", "score": 0.8886969685554504, "word": "Zalora LANGUAGES", "start": 142, "end": 158}, {"entity_group": "MISC", "score": 0.9982472658157349, "word": "Vietnamese", "start": 218, "end": 228}, {"entity_group": "MISC", "score": 0.9986990690231323, "word": "English", "start": 291, "end": 298}, {"entity_group": "MISC", "score": 0.7890761494636536, "word": "L", "start": 326, "end": 327}, {"entity_group": "ORG", "score": 0.9207910299301147, "word": "Spark", "start": 800, "end": 805}, {"entity_group": "ORG", "score": 0.9135599136352539, "word": "HDFS", "start": 811, "end": 815}, {"entity_group": "ORG", "score": 0.7715662717819214, "word": "Engineer", "start": 927, "end": 935}, {"entity_group": "MISC", "score": 0.9657636880874634, "word": "Atlassian", "start": 1199, "end": 1208}, {"entity_group": "ORG", "score": 0.9765253663063049, "word": "MCA", "start": 1647, "end": 1650}, {"entity_group": "MISC", "score": 0.7118421792984009, "word": "E", "start": 1651, "end": 1652}], "skills": {"airflow": {"years": null, "confidence": 1.0, "context": "angds121@gmail, com big data technologies (Spark, Airflow, Big Query), cloud infrastructure, and applying N", "source": "pattern_match"}, "python": {"years": null, "confidence": 1.0, "context": "support critical business Languages intelligence.\nPython Automated data migration from legacy systems to G", "source": "pattern_match"}, "java": {"years": null, "confidence": 1.0, "context": "using a CDC solution, ensuring zero downtime and Java improving data accessibility for the analytics te", "source": "pattern_match"}, "javascript": {"years": null, "confidence": 1.0, "context": "roving data accessibility for the analytics team.\nJavascript Partnered with the Data Analytics team to build a", "source": "pattern_match"}, "sql": {"years": null, "confidence": 1.0, "context": "ith the Data Analytics team to build and optimize SQL mission-critical dashboards and reports for the B", "source": "pattern_match"}, "spark": {"years": null, "confidence": 1.0, "context": "e in hoangds121@gmail, com big data technologies (Spark, Airflow, Big Query), cloud infrastructure, and a", "source": "pattern_match"}, "hdfs": {"years": null, "confidence": 1.0, "context": "hion Group 2020 - 2021 Spark Senior Data Engineer HDFS Maintained and scaled data infrastructure, develo", "source": "pattern_match"}, "redshift": {"years": null, "confidence": 1.0, "context": "and saving significant cloud computing costs.\nAWS Redshift ADIA - Lead Data Engineer 2018 - 2020 Hive Lead D", "source": "pattern_match"}, "docker": {"years": null, "confidence": 1.0, "context": "ign and development of a text-mining platform for Docker trade surveillance, processing thousands of chat", "source": "pattern_match"}, "terraform": {"years": null, "confidence": 1.0, "context": "Data Engineer 2018 - 2020 Hive Lead Data Engineer Terra<PERSON> Led the design and development of a text-mining p", "source": "pattern_match"}, "hive": {"years": null, "confidence": 1.0, "context": "WS Redshift ADIA - Lead Data Engineer 2018 - 2020 Hive Lead Data Engineer <PERSON><PERSON> Led the design and d", "source": "pattern_match"}, "aws": {"years": null, "confidence": 1.0, "context": "a processing jobs, reducing execution time by 30% AWS EMR and saving significant cloud computing costs.", "source": "pattern_match"}, "gcp": {"years": null, "confidence": 1.0, "context": "plications.\nMigrate our data platform from OCI to GCP with limited downtime.\nSKILLS Engineered and laun", "source": "pattern_match"}, "r": {"years": 10, "confidence": 0.9641854166984558, "context": "Data & Cloud Global Fashion Group", "source": "ner_entity"}, "apache spark": {"years": null, "confidence": 0.9207910299301147, "context": "Spark", "source": "ner_entity"}, "go": {"years": null, "confidence": 0.8203909397125244, "context": "Google Big Que", "source": "ner_entity"}, "mysql": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "postgresql": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "sqlite": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "kotlin": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "scala": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "matlab": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "html": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "perl": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "lua": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "haskell": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "clojure": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "angular": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "svelte": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "flask": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "laravel": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "rails": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "blazor": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "flutter": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "kotlin multiplatform": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "tensorflow": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "scikit-learn": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "matplotlib": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "plotly": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "mlflow": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "kubeflow": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "elasticsearch": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "oracle": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "influxdb": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "snowflake": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "clickhouse": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "google cloud": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "alibaba cloud": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "digitalocean": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "vercel": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "netlify": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "cloudflare": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "ansible": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "gitlab ci": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "circleci": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "helm": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "elk stack": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "new relic": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "mercurial": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "confluence": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "slack": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "linux": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "powershell": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "intellij": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "eclipse": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "selenium": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "flink": {"years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, "typescript": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "react": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "vue": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "next.js": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "express": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "asp.net": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "ember.js": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "react native": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "phonegap": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "keras": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "seaborn": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "jupyter": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "redis": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "neo4j": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "bigquery": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "couchbase": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "azure": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "heroku": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "kubernetes": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "jenkins": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "prometheus": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "teams": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "vscode": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "swagger": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "sketch": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "jest": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "cypress": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "pytest": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "unittest": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "testng": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "cucumber": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "jasmine": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "hbase": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "zookeeper": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, "parquet": {"years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}}, "categorized_skills": {"data_science_ml": [{"skill": "airflow", "years": null, "confidence": 1.0, "context": "angds121@gmail, com big data technologies (Spark, Airflow, Big Query), cloud infrastructure, and applying N", "source": "pattern_match"}, {"skill": "spark", "years": null, "confidence": 1.0, "context": "e in hoangds121@gmail, com big data technologies (Spark, Airflow, Big Query), cloud infrastructure, and a", "source": "pattern_match"}, {"skill": "tensorflow", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "scikit-learn", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "plotly", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "mlflow", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "kubeflow", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "keras", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "seaborn", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "jup<PERSON><PERSON>", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}], "programming_languages": [{"skill": "python", "years": null, "confidence": 1.0, "context": "support critical business Languages intelligence.\nPython Automated data migration from legacy systems to G", "source": "pattern_match"}, {"skill": "java", "years": null, "confidence": 1.0, "context": "using a CDC solution, ensuring zero downtime and Java improving data accessibility for the analytics te", "source": "pattern_match"}, {"skill": "javascript", "years": null, "confidence": 1.0, "context": "roving data accessibility for the analytics team.\nJavascript Partnered with the Data Analytics team to build a", "source": "pattern_match"}, {"skill": "sql", "years": null, "confidence": 1.0, "context": "ith the Data Analytics team to build and optimize SQL mission-critical dashboards and reports for the B", "source": "pattern_match"}, {"skill": "r", "years": 10, "confidence": 0.9641854166984558, "context": "Data & Cloud Global Fashion Group", "source": "ner_entity"}, {"skill": "go", "years": null, "confidence": 0.8203909397125244, "context": "Google Big Que", "source": "ner_entity"}, {"skill": "kotlin", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "scala", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "matlab", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "html", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "perl", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "lua", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "haskell", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "clojure", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "typescript", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}], "big_data": [{"skill": "hdfs", "years": null, "confidence": 1.0, "context": "hion Group 2020 - 2021 Spark Senior Data Engineer HDFS Maintained and scaled data infrastructure, develo", "source": "pattern_match"}, {"skill": "hive", "years": null, "confidence": 1.0, "context": "WS Redshift ADIA - Lead Data Engineer 2018 - 2020 Hive Lead Data Engineer <PERSON><PERSON> Led the design and d", "source": "pattern_match"}, {"skill": "apache spark", "years": null, "confidence": 0.9207910299301147, "context": "Spark", "source": "ner_entity"}, {"skill": "flink", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "hbase", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "zookeeper", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "parquet", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}], "databases": [{"skill": "redshift", "years": null, "confidence": 1.0, "context": "and saving significant cloud computing costs.\nAWS Redshift ADIA - Lead Data Engineer 2018 - 2020 Hive Lead D", "source": "pattern_match"}, {"skill": "mysql", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "postgresql", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "sqlite", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "elasticsearch", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "oracle", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "influxdb", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "snowflake", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "clickhouse", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "redis", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "neo4j", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "big<PERSON>y", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "couchbase", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}], "devops_tools": [{"skill": "docker", "years": null, "confidence": 1.0, "context": "ign and development of a text-mining platform for Docker trade surveillance, processing thousands of chat", "source": "pattern_match"}, {"skill": "terraform", "years": null, "confidence": 1.0, "context": "Data Engineer 2018 - 2020 Hive Lead Data Engineer Terra<PERSON> Led the design and development of a text-mining p", "source": "pattern_match"}, {"skill": "ansible", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "gitlab ci", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "helm", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "elk stack", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "new relic", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "kubernetes", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "jenkins", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "prometheus", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}], "cloud_platforms": [{"skill": "aws", "years": null, "confidence": 1.0, "context": "a processing jobs, reducing execution time by 30% AWS EMR and saving significant cloud computing costs.", "source": "pattern_match"}, {"skill": "gcp", "years": null, "confidence": 1.0, "context": "plications.\nMigrate our data platform from OCI to GCP with limited downtime.\nSKILLS Engineered and laun", "source": "pattern_match"}, {"skill": "google cloud", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "alibaba cloud", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "digitalocean", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "vercel", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "netlify", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "cloudflare", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "azure", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}], "web_frameworks": [{"skill": "angular", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "svelte", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "flask", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "laravel", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "rails", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "blazor", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "react", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "vue", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "next.js", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "express", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "asp.net", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "ember.js", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}], "mobile_frameworks": [{"skill": "flutter", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "kotlin multiplatform", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "react native", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "phonegap", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}], "development_tools": [{"skill": "mercurial", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "confluence", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "slack", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "linux", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "powershell", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "intellij", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "eclipse", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "teams", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "vscode", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "swagger", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "sketch", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}], "testing_frameworks": [{"skill": "selenium", "years": null, "confidence": 0.7890761494636536, "context": "L", "source": "ner_entity"}, {"skill": "jest", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "cypress", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "pytest", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "unittest", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "testng", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "cucumber", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}, {"skill": "jasmine", "years": null, "confidence": 0.7118421792984009, "context": "E", "source": "ner_entity"}]}, "additional_info": {"total_experience": null, "education": ["bachelor of software engineering lead data engineer university of science (hcmus) build an ai platform that supports building llm applications", "ms and machine learning solutions from the ground up", "mail, com big data technologies (spark, airflow, big query), cloud infrastructure, and applying nlp and sentiment analysis to solve complex business district 7, ho chi minh city problems", "mara 2021 - now bachelor of software engineering lead data engineer university of science (hcmus) build an ai platform that supports building llm applications", "marts) using open-source technologies, processing over 1tb of data daily to support critical business languages intelligence", "mated data migration from legacy systems to google big query using a cdc solution, ensuring zero downtime and java improving data accessibility for the analytics team", "making cycle", "bal fashion group 2020 - 2021 spark senior data engineer hd<PERSON> maintained and scaled data infrastructure, developing new etl airflow pipelines with airflow and emr to ingest data from multiple global google big query regions into a centralized data lake", "bs, reducing execution time by 30% aws emr and saving significant cloud computing costs", "mation from unstructured chat data, improving the efficiency of the compliance team by 40%", "machine learning system for the automated approval of customer reviews using state-of-the-art nlp models (ulmfi t, bert), reducing manual moderation workload by 75%", "mail and web platforms, which increased user engagement and drove a 10% uplift in cross-sell revenue", "back, providing actionable insights into company culture and performance across key dimensions", "management, order orchestration, and fulfillment systems", "mation from competitor websites, supporting competitive pricing strategies", "ma solution 2010 - 2011 junior developer contributed to the development of the neca project, a network monitoring solution for fiber optic cable infrastructure"], "certifications": ["aws emr and saving significant cloud computing costs", "aws redshift adia - lead data engineer 2018 - 2020 hive lead data engineer te<PERSON><PERSON> led the design and development of a text-mining platform for docker trade surveillance, processing thousands of chat logs daily to detect compliance faults and fraudulent activity"], "projects": [], "contact_info": {}}, "summary": {"total_skills_found": 102, "skills_by_category": {"data_science_ml": 11, "programming_languages": 15, "big_data": 7, "databases": 13, "devops_tools": 11, "cloud_platforms": 10, "web_frameworks": 12, "mobile_frameworks": 4, "development_tools": 11, "testing_frameworks": 8}, "category_scores": {"data_science_ml": 0.8063621304251931, "programming_languages": 0.918214209874471, "big_data": 0.8350562453269958, "databases": 0.781536762530987, "devops_tools": 0.8063621304251931, "cloud_platforms": 0.8158141255378724, "web_frameworks": 0.7504591643810272, "mobile_frameworks": 0.7504591643810272, "development_tools": 0.7609910694035616, "testing_frameworks": 0.7214964255690575}, "top_skills": [{"skill": "r", "years": 10, "confidence": 0.9641854166984558, "context": "Data & Cloud Global Fashion Group", "source": "ner_entity"}, {"skill": "airflow", "years": null, "confidence": 1.0, "context": "angds121@gmail, com big data technologies (Spark, Airflow, Big Query), cloud infrastructure, and applying N", "source": "pattern_match"}, {"skill": "spark", "years": null, "confidence": 1.0, "context": "e in hoangds121@gmail, com big data technologies (Spark, Airflow, Big Query), cloud infrastructure, and a", "source": "pattern_match"}, {"skill": "python", "years": null, "confidence": 1.0, "context": "support critical business Languages intelligence.\nPython Automated data migration from legacy systems to G", "source": "pattern_match"}, {"skill": "java", "years": null, "confidence": 1.0, "context": "using a CDC solution, ensuring zero downtime and Java improving data accessibility for the analytics te", "source": "pattern_match"}, {"skill": "javascript", "years": null, "confidence": 1.0, "context": "roving data accessibility for the analytics team.\nJavascript Partnered with the Data Analytics team to build a", "source": "pattern_match"}, {"skill": "sql", "years": null, "confidence": 1.0, "context": "ith the Data Analytics team to build and optimize SQL mission-critical dashboards and reports for the B", "source": "pattern_match"}, {"skill": "hdfs", "years": null, "confidence": 1.0, "context": "hion Group 2020 - 2021 Spark Senior Data Engineer HDFS Maintained and scaled data infrastructure, develo", "source": "pattern_match"}, {"skill": "hive", "years": null, "confidence": 1.0, "context": "WS Redshift ADIA - Lead Data Engineer 2018 - 2020 Hive Lead Data Engineer <PERSON><PERSON> Led the design and d", "source": "pattern_match"}, {"skill": "redshift", "years": null, "confidence": 1.0, "context": "and saving significant cloud computing costs.\nAWS Redshift ADIA - Lead Data Engineer 2018 - 2020 Hive Lead D", "source": "pattern_match"}], "total_experience_years": null, "education_count": 16, "certifications_count": 2, "has_contact_info": false, "strongest_category": "programming_languages"}, "personal_info": {"name": null, "email": null, "phone": null, "location": "ULMFi T, BE", "linkedin": null, "github": null, "portfolio": null, "summary": null, "objective": null}, "role_analysis": {"best_fit_role": "data_scientist", "role_rankings": [["data_scientist", 199.0], ["devops_engineer", 187.0], ["machine_learning_engineer", 184.0], ["backend_developer", 182.0], ["full_stack_developer", 174.5], ["data_engineer", 174.0], ["frontend_developer", 170.5], ["mobile_developer", 118.0]], "detailed_analysis": {"data_scientist": {"score": 199.0, "required_skills_match": ["python", "r", "sql", "scikit-learn"], "preferred_skills_match": ["tensorflow", "jup<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn"], "missing_required_skills": ["pandas", "numpy"], "missing_preferred_skills": ["pytorch"], "keyword_matches": ["machine learning"], "experience_level": "entry", "fit_percentage": 70.66666666666666, "strengths": [], "recommendations": ["Develop skills in: pandas, numpy", "Gain more experience (current: 1.2, required: 2)"], "category_scores": {"programming_languages": 37.5, "data_science_ml": 38.5, "databases": 19.5, "cloud_platforms": 10.0, "big_data": 10.5}}, "data_engineer": {"score": 174.0, "required_skills_match": ["python", "sql", "spark", "airflow"], "preferred_skills_match": ["aws", "docker", "kubernetes", "terraform"], "missing_required_skills": ["kafka"], "missing_preferred_skills": ["hadoop"], "keyword_matches": ["etl"], "experience_level": "entry", "fit_percentage": 80.0, "strengths": ["Strong foundation in data engineer core technologies"], "recommendations": ["Develop skills in: kafka", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"programming_languages": 30.0, "databases": 32.5, "big_data": 17.5, "cloud_platforms": 20.0, "devops_tools": 11.0}}, "full_stack_developer": {"score": 174.5, "required_skills_match": ["javascript", "html", "react"], "preferred_skills_match": ["typescript", "docker", "aws", "postgresql"], "missing_required_skills": ["css", "node.js"], "missing_preferred_skills": ["mongodb"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 66.0, "strengths": [], "recommendations": ["Develop skills in: css, node.js", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 37.5, "web_frameworks": 36.0, "databases": 19.5, "cloud_platforms": 15.0, "development_tools": 16.5}}, "backend_developer": {"score": 182.0, "required_skills_match": ["java", "python", "sql"], "preferred_skills_match": ["docker", "kubernetes", "aws", "redis", "postgresql"], "missing_required_skills": ["spring", "api"], "missing_preferred_skills": [], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 72.0, "strengths": [], "recommendations": ["Develop skills in: spring, api", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 45.0, "web_frameworks": 30.0, "databases": 26.0, "cloud_platforms": 15.0, "devops_tools": 11.0}}, "frontend_developer": {"score": 170.5, "required_skills_match": ["javascript", "html", "react"], "preferred_skills_match": ["typescript", "vue", "angular"], "missing_required_skills": ["css"], "missing_preferred_skills": ["webpack", "sass"], "keyword_matches": ["ui", "component"], "experience_level": "entry", "fit_percentage": 70.5, "strengths": [], "recommendations": ["Develop skills in: css", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 37.5, "web_frameworks": 48.00000000000001, "development_tools": 22.0, "testing_frameworks": 12.0}}, "devops_engineer": {"score": 187.0, "required_skills_match": ["docker", "kubernetes", "aws", "terraform", "jenkins"], "preferred_skills_match": ["ansible", "prometheus", "helm"], "missing_required_skills": [], "missing_preferred_skills": ["grafana", "istio"], "keyword_matches": ["infrastructure", "monitoring"], "experience_level": "entry", "fit_percentage": 88.0, "strengths": ["Strong foundation in devops engineer core technologies"], "recommendations": ["Gain more experience (current: 0.0, required: 2)"], "category_scores": {"devops_tools": 44.0, "cloud_platforms": 30.0, "programming_languages": 22.5, "databases": 19.5}}, "mobile_developer": {"score": 118.0, "required_skills_match": ["kotlin", "react native"], "preferred_skills_match": ["flutter"], "missing_required_skills": ["swift"], "missing_preferred_skills": ["ios", "android", "firebase", "xcode"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 52.66666666666666, "strengths": [], "recommendations": ["Develop skills in: swift", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 45.0, "mobile_frameworks": 16.0, "development_tools": 22.0, "cloud_platforms": 10.0}}, "machine_learning_engineer": {"score": 184.0, "required_skills_match": ["python", "tensorflow", "docker", "kubernetes"], "preferred_skills_match": ["mlflow", "kubeflow", "aws", "spark", "airflow"], "missing_required_skills": ["pytorch"], "missing_preferred_skills": [], "keyword_matches": ["ai"], "experience_level": "entry", "fit_percentage": 86.0, "strengths": ["Strong foundation in machine learning engineer core technologies"], "recommendations": ["Develop skills in: pytorch", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"programming_languages": 30.0, "data_science_ml": 44.0, "cloud_platforms": 20.0, "devops_tools": 22.0}}}, "top_3_roles": [["data_scientist", 199.0], ["devops_engineer", 187.0], ["machine_learning_engineer", 184.0]]}, "skill_combinations": {"technology_stacks": [{"stack": "ML Engineering Stack", "completeness": 100.0, "matched_skills": ["python", "tensorflow", "docker", "kubernetes", "aws"], "missing_skills": [], "experience_level": "novice"}, {"stack": "DevOps Stack", "completeness": 100.0, "matched_skills": ["docker", "kubernetes", "jenkins", "terraform", "aws"], "missing_skills": [], "experience_level": "novice"}, {"stack": "<PERSON><PERSON><PERSON>", "completeness": 75.0, "matched_skills": ["python", "postgresql", "redis"], "missing_skills": ["django"], "experience_level": "novice"}, {"stack": "Data Science Stack", "completeness": 60.0, "matched_skills": ["python", "scikit-learn", "jup<PERSON><PERSON>"], "missing_skills": ["pandas", "numpy"], "experience_level": "novice"}, {"stack": "MEAN", "completeness": 50.0, "matched_skills": ["express", "angular"], "missing_skills": ["mongodb", "node.js"], "experience_level": "novice"}, {"stack": "MERN", "completeness": 50.0, "matched_skills": ["express", "react"], "missing_skills": ["mongodb", "node.js"], "experience_level": "novice"}, {"stack": "LAMP", "completeness": 50.0, "matched_skills": ["linux", "mysql"], "missing_skills": ["apache", "php"], "experience_level": "novice"}, {"stack": "Spring Stack", "completeness": 50.0, "matched_skills": ["java", "mysql"], "missing_skills": ["spring", "maven"], "experience_level": "novice"}, {"stack": "React Native Stack", "completeness": 50.0, "matched_skills": ["react native", "javascript"], "missing_skills": ["redux", "firebase"], "experience_level": "novice"}, {"stack": "Flutter Stack", "completeness": 25.0, "matched_skills": ["flutter"], "missing_skills": ["dart", "firebase", "android sdk"], "experience_level": "novice"}], "skill_clusters": {"programming_languages": {"skills": [{"skill": "python", "years": null, "confidence": 1.0}, {"skill": "java", "years": null, "confidence": 1.0}, {"skill": "javascript", "years": null, "confidence": 1.0}, {"skill": "typescript", "years": null, "confidence": 0.7118421792984009}, {"skill": "go", "years": null, "confidence": 0.8203909397125244}, {"skill": "kotlin", "years": null, "confidence": 0.7890761494636536}, {"skill": "scala", "years": null, "confidence": 0.7890761494636536}, {"skill": "r", "years": 10, "confidence": 0.9641854166984558}, {"skill": "matlab", "years": null, "confidence": 0.7890761494636536}, {"skill": "sql", "years": null, "confidence": 1.0}, {"skill": "html", "years": null, "confidence": 0.7890761494636536}, {"skill": "perl", "years": null, "confidence": 0.7890761494636536}, {"skill": "lua", "years": null, "confidence": 0.7890761494636536}, {"skill": "haskell", "years": null, "confidence": 0.7890761494636536}, {"skill": "clojure", "years": null, "confidence": 0.7890761494636536}], "count": 15, "avg_experience": 0.6666666666666666, "strength_level": "strong"}, "web_frameworks": {"skills": [{"skill": "react", "years": null, "confidence": 0.7118421792984009}, {"skill": "angular", "years": null, "confidence": 0.7890761494636536}, {"skill": "vue", "years": null, "confidence": 0.7118421792984009}, {"skill": "svelte", "years": null, "confidence": 0.7890761494636536}, {"skill": "next.js", "years": null, "confidence": 0.7118421792984009}, {"skill": "flask", "years": null, "confidence": 0.7890761494636536}, {"skill": "express", "years": null, "confidence": 0.7118421792984009}, {"skill": "laravel", "years": null, "confidence": 0.7890761494636536}, {"skill": "rails", "years": null, "confidence": 0.7890761494636536}, {"skill": "asp.net", "years": null, "confidence": 0.7118421792984009}, {"skill": "blazor", "years": null, "confidence": 0.7890761494636536}, {"skill": "ember.js", "years": null, "confidence": 0.7118421792984009}], "count": 12, "avg_experience": 0.0, "strength_level": "moderate"}, "mobile_frameworks": {"skills": [{"skill": "react native", "years": null, "confidence": 0.7118421792984009}, {"skill": "flutter", "years": null, "confidence": 0.7890761494636536}, {"skill": "phonegap", "years": null, "confidence": 0.7118421792984009}, {"skill": "kotlin multiplatform", "years": null, "confidence": 0.7890761494636536}], "count": 4, "avg_experience": 0.0, "strength_level": "basic"}, "data_science_ml": {"skills": [{"skill": "tensorflow", "years": null, "confidence": 0.7890761494636536}, {"skill": "keras", "years": null, "confidence": 0.7118421792984009}, {"skill": "scikit-learn", "years": null, "confidence": 0.7890761494636536}, {"skill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "years": null, "confidence": 0.7890761494636536}, {"skill": "seaborn", "years": null, "confidence": 0.7118421792984009}, {"skill": "plotly", "years": null, "confidence": 0.7890761494636536}, {"skill": "jup<PERSON><PERSON>", "years": null, "confidence": 0.7118421792984009}, {"skill": "spark", "years": null, "confidence": 1.0}, {"skill": "airflow", "years": null, "confidence": 1.0}, {"skill": "mlflow", "years": null, "confidence": 0.7890761494636536}, {"skill": "kubeflow", "years": null, "confidence": 0.7890761494636536}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "databases": {"skills": [{"skill": "mysql", "years": null, "confidence": 0.7890761494636536}, {"skill": "postgresql", "years": null, "confidence": 0.7890761494636536}, {"skill": "redis", "years": null, "confidence": 0.7118421792984009}, {"skill": "elasticsearch", "years": null, "confidence": 0.7890761494636536}, {"skill": "oracle", "years": null, "confidence": 0.7890761494636536}, {"skill": "sqlite", "years": null, "confidence": 0.7890761494636536}, {"skill": "neo4j", "years": null, "confidence": 0.7118421792984009}, {"skill": "influxdb", "years": null, "confidence": 0.7890761494636536}, {"skill": "snowflake", "years": null, "confidence": 0.7890761494636536}, {"skill": "big<PERSON>y", "years": null, "confidence": 0.7118421792984009}, {"skill": "redshift", "years": null, "confidence": 1.0}, {"skill": "clickhouse", "years": null, "confidence": 0.7890761494636536}, {"skill": "couchbase", "years": null, "confidence": 0.7118421792984009}], "count": 13, "avg_experience": 0.0, "strength_level": "moderate"}, "cloud_platforms": {"skills": [{"skill": "aws", "years": null, "confidence": 1.0}, {"skill": "azure", "years": null, "confidence": 0.7118421792984009}, {"skill": "gcp", "years": null, "confidence": 1.0}, {"skill": "google cloud", "years": null, "confidence": 0.7890761494636536}, {"skill": "alibaba cloud", "years": null, "confidence": 0.7890761494636536}, {"skill": "digitalocean", "years": null, "confidence": 0.7890761494636536}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.7118421792984009}, {"skill": "vercel", "years": null, "confidence": 0.7890761494636536}, {"skill": "netlify", "years": null, "confidence": 0.7890761494636536}, {"skill": "cloudflare", "years": null, "confidence": 0.7890761494636536}], "count": 10, "avg_experience": 0.0, "strength_level": "moderate"}, "devops_tools": {"skills": [{"skill": "docker", "years": null, "confidence": 1.0}, {"skill": "kubernetes", "years": null, "confidence": 0.7118421792984009}, {"skill": "terraform", "years": null, "confidence": 1.0}, {"skill": "ansible", "years": null, "confidence": 0.7890761494636536}, {"skill": "jenkins", "years": null, "confidence": 0.7118421792984009}, {"skill": "gitlab ci", "years": null, "confidence": 0.7890761494636536}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.7890761494636536}, {"skill": "helm", "years": null, "confidence": 0.7890761494636536}, {"skill": "prometheus", "years": null, "confidence": 0.7118421792984009}, {"skill": "elk stack", "years": null, "confidence": 0.7890761494636536}, {"skill": "new relic", "years": null, "confidence": 0.7890761494636536}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "development_tools": {"skills": [{"skill": "mercurial", "years": null, "confidence": 0.7890761494636536}, {"skill": "confluence", "years": null, "confidence": 0.7890761494636536}, {"skill": "slack", "years": null, "confidence": 0.7890761494636536}, {"skill": "teams", "years": null, "confidence": 0.7118421792984009}, {"skill": "linux", "years": null, "confidence": 0.7890761494636536}, {"skill": "powershell", "years": null, "confidence": 0.7890761494636536}, {"skill": "vscode", "years": null, "confidence": 0.7118421792984009}, {"skill": "intellij", "years": null, "confidence": 0.7890761494636536}, {"skill": "eclipse", "years": null, "confidence": 0.7890761494636536}, {"skill": "swagger", "years": null, "confidence": 0.7118421792984009}, {"skill": "sketch", "years": null, "confidence": 0.7118421792984009}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "testing_frameworks": {"skills": [{"skill": "jest", "years": null, "confidence": 0.7118421792984009}, {"skill": "cypress", "years": null, "confidence": 0.7118421792984009}, {"skill": "selenium", "years": null, "confidence": 0.7890761494636536}, {"skill": "pytest", "years": null, "confidence": 0.7118421792984009}, {"skill": "unittest", "years": null, "confidence": 0.7118421792984009}, {"skill": "testng", "years": null, "confidence": 0.7118421792984009}, {"skill": "cucumber", "years": null, "confidence": 0.7118421792984009}, {"skill": "jasmine", "years": null, "confidence": 0.7118421792984009}], "count": 8, "avg_experience": 0.0, "strength_level": "basic"}, "big_data": {"skills": [{"skill": "apache spark", "years": null, "confidence": 0.9207910299301147}, {"skill": "flink", "years": null, "confidence": 0.7890761494636536}, {"skill": "hive", "years": null, "confidence": 1.0}, {"skill": "hbase", "years": null, "confidence": 0.7118421792984009}, {"skill": "zookeeper", "years": null, "confidence": 0.7118421792984009}, {"skill": "hdfs", "years": null, "confidence": 1.0}, {"skill": "parquet", "years": null, "confidence": 0.7118421792984009}], "count": 7, "avg_experience": 0.0, "strength_level": "basic"}}, "complementary_skills": {}, "skill_gaps": {}, "stack_completeness": {}}, "comprehensive_report": {"candidate_profile": {"personal_info": {"name": null, "email": null, "phone": null, "location": "ULMFi T, BE", "linkedin": null, "github": null, "portfolio": null, "summary": null, "objective": null}, "professional_summary": "None appears to be best suited for a Data Scientist role. Strong background in programming languages with 15 identified skills. Key expertise includes r, airflow, python. ", "contact_completeness": {"required_complete": 0, "required_total": 3, "optional_complete": 1, "optional_total": 4, "missing_required": ["name", "email", "phone"], "missing_optional": ["linkedin", "github", "portfolio"], "required_percentage": 0.0, "optional_percentage": 25.0, "overall_score": 7.5}}, "role_recommendations": {"best_fit": "data_scientist", "top_matches": [["data_scientist", 199.0], ["devops_engineer", 187.0], ["machine_learning_engineer", 184.0]], "detailed_fit_analysis": {"data_scientist": {"score": 199.0, "required_skills_match": ["python", "r", "sql", "scikit-learn"], "preferred_skills_match": ["tensorflow", "jup<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn"], "missing_required_skills": ["pandas", "numpy"], "missing_preferred_skills": ["pytorch"], "keyword_matches": ["machine learning"], "experience_level": "entry", "fit_percentage": 70.66666666666666, "strengths": [], "recommendations": ["Develop skills in: pandas, numpy", "Gain more experience (current: 1.2, required: 2)"], "category_scores": {"programming_languages": 37.5, "data_science_ml": 38.5, "databases": 19.5, "cloud_platforms": 10.0, "big_data": 10.5}}, "data_engineer": {"score": 174.0, "required_skills_match": ["python", "sql", "spark", "airflow"], "preferred_skills_match": ["aws", "docker", "kubernetes", "terraform"], "missing_required_skills": ["kafka"], "missing_preferred_skills": ["hadoop"], "keyword_matches": ["etl"], "experience_level": "entry", "fit_percentage": 80.0, "strengths": ["Strong foundation in data engineer core technologies"], "recommendations": ["Develop skills in: kafka", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"programming_languages": 30.0, "databases": 32.5, "big_data": 17.5, "cloud_platforms": 20.0, "devops_tools": 11.0}}, "full_stack_developer": {"score": 174.5, "required_skills_match": ["javascript", "html", "react"], "preferred_skills_match": ["typescript", "docker", "aws", "postgresql"], "missing_required_skills": ["css", "node.js"], "missing_preferred_skills": ["mongodb"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 66.0, "strengths": [], "recommendations": ["Develop skills in: css, node.js", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 37.5, "web_frameworks": 36.0, "databases": 19.5, "cloud_platforms": 15.0, "development_tools": 16.5}}, "backend_developer": {"score": 182.0, "required_skills_match": ["java", "python", "sql"], "preferred_skills_match": ["docker", "kubernetes", "aws", "redis", "postgresql"], "missing_required_skills": ["spring", "api"], "missing_preferred_skills": [], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 72.0, "strengths": [], "recommendations": ["Develop skills in: spring, api", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 45.0, "web_frameworks": 30.0, "databases": 26.0, "cloud_platforms": 15.0, "devops_tools": 11.0}}, "frontend_developer": {"score": 170.5, "required_skills_match": ["javascript", "html", "react"], "preferred_skills_match": ["typescript", "vue", "angular"], "missing_required_skills": ["css"], "missing_preferred_skills": ["webpack", "sass"], "keyword_matches": ["ui", "component"], "experience_level": "entry", "fit_percentage": 70.5, "strengths": [], "recommendations": ["Develop skills in: css", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 37.5, "web_frameworks": 48.00000000000001, "development_tools": 22.0, "testing_frameworks": 12.0}}, "devops_engineer": {"score": 187.0, "required_skills_match": ["docker", "kubernetes", "aws", "terraform", "jenkins"], "preferred_skills_match": ["ansible", "prometheus", "helm"], "missing_required_skills": [], "missing_preferred_skills": ["grafana", "istio"], "keyword_matches": ["infrastructure", "monitoring"], "experience_level": "entry", "fit_percentage": 88.0, "strengths": ["Strong foundation in devops engineer core technologies"], "recommendations": ["Gain more experience (current: 0.0, required: 2)"], "category_scores": {"devops_tools": 44.0, "cloud_platforms": 30.0, "programming_languages": 22.5, "databases": 19.5}}, "mobile_developer": {"score": 118.0, "required_skills_match": ["kotlin", "react native"], "preferred_skills_match": ["flutter"], "missing_required_skills": ["swift"], "missing_preferred_skills": ["ios", "android", "firebase", "xcode"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 52.66666666666666, "strengths": [], "recommendations": ["Develop skills in: swift", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 45.0, "mobile_frameworks": 16.0, "development_tools": 22.0, "cloud_platforms": 10.0}}, "machine_learning_engineer": {"score": 184.0, "required_skills_match": ["python", "tensorflow", "docker", "kubernetes"], "preferred_skills_match": ["mlflow", "kubeflow", "aws", "spark", "airflow"], "missing_required_skills": ["pytorch"], "missing_preferred_skills": [], "keyword_matches": ["ai"], "experience_level": "entry", "fit_percentage": 86.0, "strengths": ["Strong foundation in machine learning engineer core technologies"], "recommendations": ["Develop skills in: pytorch", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"programming_languages": 30.0, "data_science_ml": 44.0, "cloud_platforms": 20.0, "devops_tools": 22.0}}}, "career_progression": {"current_level": "entry", "next_roles": ["Mid-level data scientist", "Senior data scientist (2-3 years)", "Technical Lead (4-5 years)"], "progression_timeline": {"1-2 years": "Focus on deepening data scientist skills", "2-4 years": "<PERSON>ain experience in system design and mentoring", "4+ years": "Consider technical leadership or specialization"}, "skill_requirements": {"technical": ["system design basics", "testing frameworks", "ci/cd"], "soft_skills": ["communication", "collaboration", "problem solving"]}, "leadership_readiness": true}}, "technical_assessment": {"skill_summary": {"total_skills": 102, "skills_with_experience": 1, "average_experience": 10.0, "experience_distribution": {"expert": 1, "advanced": 0, "intermediate": 0, "beginner": 0, "novice": 101}, "most_experienced_skill": "r"}, "technology_stacks": [{"stack": "ML Engineering Stack", "completeness": 100.0, "matched_skills": ["python", "tensorflow", "docker", "kubernetes", "aws"], "missing_skills": [], "experience_level": "novice"}, {"stack": "DevOps Stack", "completeness": 100.0, "matched_skills": ["docker", "kubernetes", "jenkins", "terraform", "aws"], "missing_skills": [], "experience_level": "novice"}, {"stack": "<PERSON><PERSON><PERSON>", "completeness": 75.0, "matched_skills": ["python", "postgresql", "redis"], "missing_skills": ["django"], "experience_level": "novice"}, {"stack": "Data Science Stack", "completeness": 60.0, "matched_skills": ["python", "scikit-learn", "jup<PERSON><PERSON>"], "missing_skills": ["pandas", "numpy"], "experience_level": "novice"}, {"stack": "MEAN", "completeness": 50.0, "matched_skills": ["express", "angular"], "missing_skills": ["mongodb", "node.js"], "experience_level": "novice"}, {"stack": "MERN", "completeness": 50.0, "matched_skills": ["express", "react"], "missing_skills": ["mongodb", "node.js"], "experience_level": "novice"}, {"stack": "LAMP", "completeness": 50.0, "matched_skills": ["linux", "mysql"], "missing_skills": ["apache", "php"], "experience_level": "novice"}, {"stack": "Spring Stack", "completeness": 50.0, "matched_skills": ["java", "mysql"], "missing_skills": ["spring", "maven"], "experience_level": "novice"}, {"stack": "React Native Stack", "completeness": 50.0, "matched_skills": ["react native", "javascript"], "missing_skills": ["redux", "firebase"], "experience_level": "novice"}, {"stack": "Flutter Stack", "completeness": 25.0, "matched_skills": ["flutter"], "missing_skills": ["dart", "firebase", "android sdk"], "experience_level": "novice"}], "skill_clusters": {"programming_languages": {"skills": [{"skill": "python", "years": null, "confidence": 1.0}, {"skill": "java", "years": null, "confidence": 1.0}, {"skill": "javascript", "years": null, "confidence": 1.0}, {"skill": "typescript", "years": null, "confidence": 0.7118421792984009}, {"skill": "go", "years": null, "confidence": 0.8203909397125244}, {"skill": "kotlin", "years": null, "confidence": 0.7890761494636536}, {"skill": "scala", "years": null, "confidence": 0.7890761494636536}, {"skill": "r", "years": 10, "confidence": 0.9641854166984558}, {"skill": "matlab", "years": null, "confidence": 0.7890761494636536}, {"skill": "sql", "years": null, "confidence": 1.0}, {"skill": "html", "years": null, "confidence": 0.7890761494636536}, {"skill": "perl", "years": null, "confidence": 0.7890761494636536}, {"skill": "lua", "years": null, "confidence": 0.7890761494636536}, {"skill": "haskell", "years": null, "confidence": 0.7890761494636536}, {"skill": "clojure", "years": null, "confidence": 0.7890761494636536}], "count": 15, "avg_experience": 0.6666666666666666, "strength_level": "strong"}, "web_frameworks": {"skills": [{"skill": "react", "years": null, "confidence": 0.7118421792984009}, {"skill": "angular", "years": null, "confidence": 0.7890761494636536}, {"skill": "vue", "years": null, "confidence": 0.7118421792984009}, {"skill": "svelte", "years": null, "confidence": 0.7890761494636536}, {"skill": "next.js", "years": null, "confidence": 0.7118421792984009}, {"skill": "flask", "years": null, "confidence": 0.7890761494636536}, {"skill": "express", "years": null, "confidence": 0.7118421792984009}, {"skill": "laravel", "years": null, "confidence": 0.7890761494636536}, {"skill": "rails", "years": null, "confidence": 0.7890761494636536}, {"skill": "asp.net", "years": null, "confidence": 0.7118421792984009}, {"skill": "blazor", "years": null, "confidence": 0.7890761494636536}, {"skill": "ember.js", "years": null, "confidence": 0.7118421792984009}], "count": 12, "avg_experience": 0.0, "strength_level": "moderate"}, "mobile_frameworks": {"skills": [{"skill": "react native", "years": null, "confidence": 0.7118421792984009}, {"skill": "flutter", "years": null, "confidence": 0.7890761494636536}, {"skill": "phonegap", "years": null, "confidence": 0.7118421792984009}, {"skill": "kotlin multiplatform", "years": null, "confidence": 0.7890761494636536}], "count": 4, "avg_experience": 0.0, "strength_level": "basic"}, "data_science_ml": {"skills": [{"skill": "tensorflow", "years": null, "confidence": 0.7890761494636536}, {"skill": "keras", "years": null, "confidence": 0.7118421792984009}, {"skill": "scikit-learn", "years": null, "confidence": 0.7890761494636536}, {"skill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "years": null, "confidence": 0.7890761494636536}, {"skill": "seaborn", "years": null, "confidence": 0.7118421792984009}, {"skill": "plotly", "years": null, "confidence": 0.7890761494636536}, {"skill": "jup<PERSON><PERSON>", "years": null, "confidence": 0.7118421792984009}, {"skill": "spark", "years": null, "confidence": 1.0}, {"skill": "airflow", "years": null, "confidence": 1.0}, {"skill": "mlflow", "years": null, "confidence": 0.7890761494636536}, {"skill": "kubeflow", "years": null, "confidence": 0.7890761494636536}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "databases": {"skills": [{"skill": "mysql", "years": null, "confidence": 0.7890761494636536}, {"skill": "postgresql", "years": null, "confidence": 0.7890761494636536}, {"skill": "redis", "years": null, "confidence": 0.7118421792984009}, {"skill": "elasticsearch", "years": null, "confidence": 0.7890761494636536}, {"skill": "oracle", "years": null, "confidence": 0.7890761494636536}, {"skill": "sqlite", "years": null, "confidence": 0.7890761494636536}, {"skill": "neo4j", "years": null, "confidence": 0.7118421792984009}, {"skill": "influxdb", "years": null, "confidence": 0.7890761494636536}, {"skill": "snowflake", "years": null, "confidence": 0.7890761494636536}, {"skill": "big<PERSON>y", "years": null, "confidence": 0.7118421792984009}, {"skill": "redshift", "years": null, "confidence": 1.0}, {"skill": "clickhouse", "years": null, "confidence": 0.7890761494636536}, {"skill": "couchbase", "years": null, "confidence": 0.7118421792984009}], "count": 13, "avg_experience": 0.0, "strength_level": "moderate"}, "cloud_platforms": {"skills": [{"skill": "aws", "years": null, "confidence": 1.0}, {"skill": "azure", "years": null, "confidence": 0.7118421792984009}, {"skill": "gcp", "years": null, "confidence": 1.0}, {"skill": "google cloud", "years": null, "confidence": 0.7890761494636536}, {"skill": "alibaba cloud", "years": null, "confidence": 0.7890761494636536}, {"skill": "digitalocean", "years": null, "confidence": 0.7890761494636536}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.7118421792984009}, {"skill": "vercel", "years": null, "confidence": 0.7890761494636536}, {"skill": "netlify", "years": null, "confidence": 0.7890761494636536}, {"skill": "cloudflare", "years": null, "confidence": 0.7890761494636536}], "count": 10, "avg_experience": 0.0, "strength_level": "moderate"}, "devops_tools": {"skills": [{"skill": "docker", "years": null, "confidence": 1.0}, {"skill": "kubernetes", "years": null, "confidence": 0.7118421792984009}, {"skill": "terraform", "years": null, "confidence": 1.0}, {"skill": "ansible", "years": null, "confidence": 0.7890761494636536}, {"skill": "jenkins", "years": null, "confidence": 0.7118421792984009}, {"skill": "gitlab ci", "years": null, "confidence": 0.7890761494636536}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.7890761494636536}, {"skill": "helm", "years": null, "confidence": 0.7890761494636536}, {"skill": "prometheus", "years": null, "confidence": 0.7118421792984009}, {"skill": "elk stack", "years": null, "confidence": 0.7890761494636536}, {"skill": "new relic", "years": null, "confidence": 0.7890761494636536}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "development_tools": {"skills": [{"skill": "mercurial", "years": null, "confidence": 0.7890761494636536}, {"skill": "confluence", "years": null, "confidence": 0.7890761494636536}, {"skill": "slack", "years": null, "confidence": 0.7890761494636536}, {"skill": "teams", "years": null, "confidence": 0.7118421792984009}, {"skill": "linux", "years": null, "confidence": 0.7890761494636536}, {"skill": "powershell", "years": null, "confidence": 0.7890761494636536}, {"skill": "vscode", "years": null, "confidence": 0.7118421792984009}, {"skill": "intellij", "years": null, "confidence": 0.7890761494636536}, {"skill": "eclipse", "years": null, "confidence": 0.7890761494636536}, {"skill": "swagger", "years": null, "confidence": 0.7118421792984009}, {"skill": "sketch", "years": null, "confidence": 0.7118421792984009}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "testing_frameworks": {"skills": [{"skill": "jest", "years": null, "confidence": 0.7118421792984009}, {"skill": "cypress", "years": null, "confidence": 0.7118421792984009}, {"skill": "selenium", "years": null, "confidence": 0.7890761494636536}, {"skill": "pytest", "years": null, "confidence": 0.7118421792984009}, {"skill": "unittest", "years": null, "confidence": 0.7118421792984009}, {"skill": "testng", "years": null, "confidence": 0.7118421792984009}, {"skill": "cucumber", "years": null, "confidence": 0.7118421792984009}, {"skill": "jasmine", "years": null, "confidence": 0.7118421792984009}], "count": 8, "avg_experience": 0.0, "strength_level": "basic"}, "big_data": {"skills": [{"skill": "apache spark", "years": null, "confidence": 0.9207910299301147}, {"skill": "flink", "years": null, "confidence": 0.7890761494636536}, {"skill": "hive", "years": null, "confidence": 1.0}, {"skill": "hbase", "years": null, "confidence": 0.7118421792984009}, {"skill": "zookeeper", "years": null, "confidence": 0.7118421792984009}, {"skill": "hdfs", "years": null, "confidence": 1.0}, {"skill": "parquet", "years": null, "confidence": 0.7118421792984009}], "count": 7, "avg_experience": 0.0, "strength_level": "basic"}}, "technical_strengths": ["Complete mastery of ML Engineering Stack technology stack", "Strong programming languages expertise with 15 skills", "Expert-level experience in r"], "skill_gaps": {"data_scientist": {"critical_gaps": ["pandas", "numpy"], "enhancement_opportunities": ["pytorch"]}, "data_engineer": {"critical_gaps": ["kafka"], "enhancement_opportunities": ["hadoop"]}, "full_stack_developer": {"critical_gaps": ["css", "node.js"], "enhancement_opportunities": ["mongodb"]}}}, "experience_analysis": {"total_experience": null, "experience_distribution": {"mean_years": 0.09803921568627451, "median_years": 0.0, "std_deviation": 0.9852819236393031, "max_years": 10, "min_years": 0, "distribution_type": "balanced"}, "seniority_indicators": {"leadership_keywords": 0, "architecture_keywords": 0, "mentoring_keywords": 0, "project_management_keywords": 0, "senior_level_skills": 2, "overall_level": "junior", "seniority_score": 0.6294117647058823}, "learning_trajectory": "continuous_learner"}, "recommendations": {"immediate_actions": ["Priority: Learn pandas to meet core requirements for data scientist", "Create a GitHub portfolio showcasing your technical skills"], "skill_development": {"short_term": ["pandas", "numpy"], "medium_term": ["pytorch"], "long_term": ["microservices", "system design", "machine learning"]}, "career_advice": ["Focus on gaining hands-on experience in data scientist through internships, projects, or entry-level positions", "Your skills align with multiple roles: consider data scientist, devops engineer"]}, "overall_assessment": {"marketability_score": {"overall_score": 76.19999999999999, "components": {"skill_diversity": 100, "experience_depth": 50.0, "role_fit": 70.66666666666666, "modern_skills": 100}, "grade": "B+", "market_readiness": "medium"}, "unique_value_proposition": "Full-stack expertise in ML Engineering Stack with 100% stack completeness", "competitive_advantages": ["Proficiency in high-demand technologies: kubernetes, aws, react", "Versatility to work in multiple roles: data scientist and devops engineer", "Broad technical knowledge across 102 different technologies"]}}}