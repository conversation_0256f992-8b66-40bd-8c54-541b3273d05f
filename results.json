{
  "file_path": "Hoang Phan CV.pdf",
  "timestamp": "2025-06-23T20:54:55.307823",
  "success": true,
  "error": null,
  "extraction_method": "pdfplumber",
  "raw_text": "HOANG PHAN\nCONTACT PROFILE\nResults-driven and innovative Lead Data Engineer with over 10 years\n+**************\nof experience designing, building, and optimizing data platforms and\nmachine learning solutions from the ground up. Proven expertise in\nhoangds121@gmail,com\nbig data technologies (Spark, Airflow, BigQuery), cloud infrastructure,\nand applying NLP and sentiment analysis to solve complex business\nDistrict 7, Ho Chi Minh City\nproblems. Seeking to leverage deep technical knowledge to drive data\ninnovation in a challenging senior engineering role.\nEDUCATION WORK EXPERIENCE\nTamara 2021 - NOW\nBachelor of Software Engineering\nLead Data Engineer\nUniversity of Science (HCMUS) Build an AI platform that supports building LLM applications.\nMigrate our data platform from OCI to GCP with limited downtime.\nSKILLS Engineered and launched a comprehensive data platform from\nscratch (DataLake, DataMarts) using open-source technologies,\nprocessing over 1TB of data daily to support critical business\nLanguages\nintelligence.\nPython Automated data migration from legacy systems to Google\nBigQuery using a CDC solution, ensuring zero downtime and\nJava\nimproving data accessibility for the analytics team.\nJavascript\nPartnered with the Data Analytics team to build and optimize\nSQL mission-critical dashboards and reports for the Board of Directors,\nleading to a 20% faster decision-making cycle.\nBig Data & Cloud\nGlobal Fashion Group 2020 - 2021\nSpark\nSenior Data Engineer\nHDFS\nMaintained and scaled data infrastructure, developing new ETL\nAirflow\npipelines with Airflow and EMR to ingest data from multiple global\nGoogle BigQuery regions into a centralized data lake.\nOptimized data processing jobs, reducing execution time by 30%\nAWS EMR\nand saving significant cloud computing costs.\nAWS Redshift\nADIA - Lead Data Engineer 2018 - 2020\nHive Lead Data Engineer\nTerraform\nLed the design and development of a text-mining platform for\nDocker trade surveillance, processing thousands of chat logs daily to\ndetect compliance faults and fraudulent activity.\nExtracted and structured critical transaction information from\nunstructured chat data, improving the efficiency of the compliance\nteam by 40%.\nZalora\nLANGUAGES Senior Data Engineer 2016 - 2018\nData Engineer 2015 - 2016\nVietnamese\nDeveloped and integrated a personalized search solution using\nEnglish (Fluent)\nLearning to Rank (LTR) models into SOLR, increasing search\nconversion rates by 15%.\nEngineered a machine learning system for the automated approval\nof customer reviews using state-of-the-art NLP models (ULMFiT,\nBERT), reducing manual moderation workload by 75%.\nBuilt and deployed an item-item recommendation engine for email\nand web platforms, which increased user engagement and drove a\n10% uplift in cross-sell revenue.\nDesigned and constructed a scalable Data Science Platform using\nMesos, Spark, and HDFS, empowering the data science team to\nexperiment and deploy models more efficiently.\nTinyPulse 2014 - 2015\nData Engineer\nDeveloped a sentiment analysis engine to process employee\nfeedback, providing actionable insights into company culture and\nperformance across key dimensions.\nCreated custom, data-driven reports for enterprise customers,\nenhancing client satisfaction and retention.\nAtlassian 2014 - 2014\nSenior Developer\nOperated in a hybrid growth developer and data analyst role,\ndesigning and implementing A/B tests to validate new product\nhypotheses and evaluate feature impact.\nQueried and analyzed large datasets from Redshift and Hive to\nuncover user behavior patterns, presenting findings through\nvisualizations to inform product strategy.\nMobivi 2011 - 2014\nSenior Developer\nArchitected and developed key components of the MCA E-\nCommerce platform, including the inventory management, order\norchestration, and fulfillment systems.\nBuilt a distributed web crawling system to collect and process\nonline product information from competitor websites, supporting\ncompetitive pricing strategies.\nTMA Solution 2010 - 2011\nJunior Developer\nContributed to the development of the NECA project, a network\nmonitoring solution for fiber optic cable infrastructure.",
  "entities": [
    {
      "entity_group": "ORG",
      "score": 