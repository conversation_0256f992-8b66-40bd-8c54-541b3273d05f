{"file_path": "Hoang Phan CV.pdf", "timestamp": "2025-06-23T21:52:45.417778", "success": true, "error": null, "extraction_method": "pypdf2", "raw_text": "EDUCATIONCONTACT SKILLS+84 935 412 737 hoangds121@gmail, com District 7, Ho Chi Minh City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflow Google Big Query AWS EMR AWS Redshift Hive Terraform Docker HOANG PHAN W ORK EXPERIENCEPROFILE Results-driven and innovative Lead Data Engineer with over 10 years of experience designing, building, and optimizing data platforms and machine learning solutions from the ground up.\nProven expertise in big data technologies (Spark, Airflow, Big Query), cloud infrastructure, and applying NLP and sentiment analysis to solve complex business problems.\nSeeking to leverage deep technical knowledge to drive data innovation in a challenging senior engineering role. 2021 - NO<PERSON> Tamara Lead Data Engineer 2020 - 2021 Global Fashion Group Senior Data Engineer 2018 - 2020 ADIA - Lead Data Engineer Lead Data Engineer Bachelor of Software Engineering University of Science (HCMUS) Build an AI platform that supports building LLM applications.\nMigrate our data platform from OCI to GCP with limited downtime.\nEngineered and launched a comprehensive data platform from scratch (Data Lake, Data Marts) using open-source technologies, processing over 1TB of data daily to support critical business intelligence.\nAutomated data migration from legacy systems to Google Big Query using a CDC solution, ensuring zero downtime and improving data accessibility for the analytics team.\nPartnered with the Data Analytics team to build and optimize mission-critical dashboards and reports for the Board of Directors, leading to a 20% faster decision-making cycle.\nMaintained and scaled data infrastructure, developing new ETL pipelines with Airflow and EMR to ingest data from multiple global regions into a centralized data lake.\nOptimized data processing jobs, reducing execution time by 30% and saving significant cloud computing costs.\nLed the design and development of a text-mining platform for trade surveillance, processing thousands of chat logs daily to detect compliance faults and fraudulent activity.\nExtracted and structured critical transaction information from unstructured chat data, improving the efficiency of the compliance team by 40%.\nZalora Senior Data Engineer Developed and integrated a personalized search solution using Learning to Rank (LTR) models into SOLR, increasing search conversion rates by 15%.\nEngineered a machine learning system for the automated approval of customer reviews using state-of-the-art NLP models (ULMFi T, BERT), reducing manual moderation workload by 75%.\nBuilt and deployed an item-item recommendation engine for email and web platforms, which increased user engagement and drove a 10% uplift in cross-sell revenue.\nDesigned and constructed a scalable Data Science Platform using Mesos, Spark, and HDFS, empowering the data science team to experiment and deploy models more efficiently. 2014 - 2015 Tiny Pulse Data Engineer Developed a sentiment analysis engine to process employee feedback, providing actionable insights into company culture and performance across key dimensions.\nCreated custom, data-driven reports for enterprise customers, enhancing client satisfaction and retention. 2016 - 2018 Data Engineer 2015 - 2016 2014 - 2014 Atlassian Senior Developer Operated in a hybrid growth developer and data analyst role, designing and implementing A/B tests to validate new product hypotheses and evaluate feature impact.\nQueried and analyzed large datasets from Redshift and Hive to uncover user behavior patterns, presenting findings through visualizations to inform product strategy. 2011 - 2014 Mobivi Senior Developer Architected and developed key components of the MCA E- Commerce platform, including the inventory management, order orchestration, and fulfillment systems.\nBuilt a distributed web crawling system to collect and process online product information from competitor websites, supporting competitive pricing strategies. 2010 - 2011 TMA Solution Junior Developer Contributed to the development of the NECA project, a network monitoring solution for fiber optic cable infrastructure.\nLANGUAGES Vietnamese English (Fluent)", "entities": [{"entity_group": "LOC", "score": 0.9985169172286987, "word": "Ho Chi Minh City", "start": 73, "end": 89}, {"entity_group": "MISC", "score": 0.8570629954338074, "word": "Languages Python Java Javascript S", "start": 90, "end": 124}, {"entity_group": "MISC", "score": 0.8077688217163086, "word": "Google", "start": 163, "end": 169}, {"entity_group": "ORG", "score": 0.7847576141357422, "word": "<PERSON>er", "start": 216, "end": 222}, {"entity_group": "ORG", "score": 0.885463535785675, "word": "Global Fashion Group", "start": 769, "end": 789}, {"entity_group": "ORG", "score": 0.9649661779403687, "word": "Software Engineering University of Science", "start": 880, "end": 922}, {"entity_group": "ORG", "score": 0.9362217783927917, "word": "HCMUS", "start": 924, "end": 929}, {"entity_group": "ORG", "score": 0.7246823310852051, "word": "OCI", "start": 1023, "end": 1026}, {"entity_group": "ORG", "score": 0.7413449287414551, "word": "Data Mart", "start": 1135, "end": 1144}, {"entity_group": "MISC", "score": 0.7953106760978699, "word": "Google", "start": 1303, "end": 1309}, {"entity_group": "ORG", "score": 0.8844799995422363, "word": "CDC", "start": 1328, "end": 1331}, {"entity_group": "ORG", "score": 0.9812588095664978, "word": "Data Analytics", "start": 1440, "end": 1454}, {"entity_group": "ORG", "score": 0.7860682606697083, "word": "Board of Directors", "start": 1530, "end": 1548}, {"entity_group": "ORG", "score": 0.7131471037864685, "word": "EMR", "start": 1685, "end": 1688}, {"entity_group": "ORG", "score": 0.8877021670341492, "word": "Zalora", "start": 142, "end": 148}, {"entity_group": "MISC", "score": 0.7519528865814209, "word": "Learning to Rank", "start": 232, "end": 248}, {"entity_group": "MISC", "score": 0.8887915015220642, "word": "L", "start": 250, "end": 251}, {"entity_group": "MISC", "score": 0.7046172022819519, "word": "Data", "start": 689, "end": 693}, {"entity_group": "MISC", "score": 0.8223502039909363, "word": "Platform", "start": 702, "end": 710}, {"entity_group": "ORG", "score": 0.7068102359771729, "word": "Me", "start": 717, "end": 719}, {"entity_group": "ORG", "score": 0.9442412853240967, "word": "Spark", "start": 724, "end": 729}, {"entity_group": "ORG", "score": 0.9497920274734497, "word": "HDFS", "start": 735, "end": 739}, {"entity_group": "ORG", "score": 0.7859410047531128, "word": "Pulse Data Engineer", "start": 840, "end": 859}, {"entity_group": "ORG", "score": 0.710976779460907, "word": "Atlassian", "start": 1173, "end": 1182}, {"entity_group": "ORG", "score": 0.8072088360786438, "word": "<PERSON><PERSON><PERSON>", "start": 1537, "end": 1543}, {"entity_group": "ORG", "score": 0.9736388921737671, "word": "MCA", "start": 1609, "end": 1612}, {"entity_group": "ORG", "score": 0.833426833152771, "word": "NECA", "start": 1954, "end": 1958}, {"entity_group": "MISC", "score": 0.9933662414550781, "word": "Vietnamese English", "start": 10, "end": 28}], "skills": {"javascript": {"years": null, "confidence": 1.0, "context": "istrict 7, Ho <PERSON> City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflow Google Bi", "source": "pattern_match"}, "python": {"years": null, "confidence": 1.0, "context": "gmail, com District 7, Ho <PERSON> Minh City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS A", "source": "pattern_match"}, "java": {"years": null, "confidence": 1.0, "context": "com District 7, Ho Chi Minh City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflo", "source": "pattern_match"}, "docker": {"years": null, "confidence": 1.0, "context": "gle Big Query AWS EMR AWS Redshift Hive Terraform Docker HOANG PHAN W ORK EXPERIENCEPROFILE Results-driven", "source": "pattern_match"}, "sql": {"years": null, "confidence": 1.0, "context": "Ho Chi Minh City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflow Google Big Qu", "source": "pattern_match"}, "solr": {"years": null, "confidence": 1.0, "context": "solution using Learning to Rank (LTR) models into SOLR, increasing search conversion rates by 15%.\nEngin", "source": "pattern_match"}, "mesos": {"years": null, "confidence": 1.0, "context": "onstructed a scalable Data Science Platform using Mesos, Spark, and HDFS, empowering the data science tea", "source": "pattern_match"}, "spark": {"years": null, "confidence": 1.0, "context": "uages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflow Google Big Query AWS EMR AWS Redshif", "source": "pattern_match"}, "hdfs": {"years": null, "confidence": 1.0, "context": "Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflow Google Big Query AWS EMR AWS Redshift Hiv", "source": "pattern_match"}, "fiber": {"years": null, "confidence": 1.0, "context": "e NECA project, a network monitoring solution for fiber optic cable infrastructure.\nLANGUAGES Vietnamese", "source": "pattern_match"}, "bert": {"years": null, "confidence": 1.0, "context": "views using state-of-the-art NLP models (ULMFi T, BERT), reducing manual moderation workload by 75%.\nBui", "source": "pattern_match"}, "hive": {"years": null, "confidence": 1.0, "context": "DFS Airflow Google Big Query AWS EMR AWS Redshift Hive Terraform Docker HOANG PHAN W ORK EXPERIENCEPROFI", "source": "pattern_match"}, "aws": {"years": null, "confidence": 1.0, "context": "Data & Cloud Spark HDFS Airflow Google Big Query AWS EMR AWS Redshift Hive Terraform Docker HOANG PHAN", "source": "pattern_match"}, "gcp": {"years": null, "confidence": 1.0, "context": "plications.\nMigrate our data platform from OCI to GCP with limited downtime.\nEngineered and launched a", "source": "pattern_match"}, "terraform": {"years": null, "confidence": 1.0, "context": "irflow Google Big Query AWS EMR AWS Redshift Hive Terraform Docker HOANG PHAN W ORK EXPERIENCEPROFILE Results", "source": "pattern_match"}, "c": {"years": 10, "confidence": 0.9985169172286987, "context": "Ho Chi Minh City", "source": "ner_entity"}, "v": {"years": 1, "confidence": 0.9933662414550781, "context": "Vietnamese English", "source": "ner_entity"}, "memcached": {"years": null, "confidence": 0.9736388921737671, "context": "MCA", "source": "ner_entity"}, "r": {"years": 10, "confidence": 0.9649661779403687, "context": "Software Engineering University of Science", "source": "ner_entity"}, "gin": {"years": 1, "confidence": 0.9649661779403687, "context": "Software Engineering University of Science", "source": "ner_entity"}, "spark sql": {"years": null, "confidence": 0.9442412853240967, "context": "Spark", "source": "ner_entity"}, "pyspark": {"years": null, "confidence": 0.9442412853240967, "context": "Spark", "source": "ner_entity"}, "apache spark": {"years": null, "confidence": 0.9442412853240967, "context": "Spark", "source": "ner_entity"}, "google colab": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "google bigtable": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "google bigquery": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "google cloud platform": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "google cloud": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "google cloud functions": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "google cloud run": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "google gke": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "google meet": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "google pub/sub": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "google cloud storage": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "lora": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "kotlin": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "scala": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "matlab": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "html": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "less": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "stylus": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "assembly": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "webassembly": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "haskell": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "clojure": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "erlang": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "elixir": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "ocaml": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "lisp": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "powershell": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "perl": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "lua": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "tcl": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "plsql": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "t-sql": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "nosql": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "solidity": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "julia": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "crystal": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "angular": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "angularjs": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "svelte": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "sveltekit": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "solid.js": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "lit": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "stencil": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "tailwind css": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "bulma": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "material ui": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "styled-components": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "flask": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "bottle": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "falcon": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "starlette": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "play framework": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "blazor": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "laravel": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "phalcon": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "rails": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "ruby on rails": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "revel": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "celery": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "gradle": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "flutter": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "kotlin multiplatform": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "unreal engine": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "tensorflow": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "scikit-learn": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "lightgbm": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "mlflow": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "kubeflow": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "polars": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "matplotlib": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "plotly": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "altair": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "tableau": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "looker": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "llama": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "stable diffusion": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "pillow": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "albumentations": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "seldon": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "bentoml": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "tensorflow serving": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "apache airflow": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "jupyterlab": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "kaggle": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "flink": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "mysql": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "postgresql": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "sqlite": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "oracle": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "sql server": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "leveldb": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "amazon simpledb": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "influxdb": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "timescaledb": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "elasticsearch": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "algolia": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "snowflake": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "clickhouse": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "voltdb": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "alibaba cloud": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "tencent cloud": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "oracle cloud": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "ibm cloud": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "digitalocean": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "linode": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "vultr": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "vercel": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "netlify": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "railway": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "fly.io": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "cloudflare pages": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "aws lambda": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "cloudflare workers": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "deno deploy": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "buildah": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "helm": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "linkerd": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "ansible": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "saltstack": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "pulumi": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "cloudformation": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "arm templates": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "gitlab ci": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "circleci": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "buildkite": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "flux": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "elk stack": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "logstash": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "new relic": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "splunk": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "opentelemetry": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "fluentd": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "fluent bit": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "vault": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "consul": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "falco": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "twistlock": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "gitlab": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "mercurial": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "visual studio": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "intellij idea": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "eclipse": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "sublime text": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "confluence": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "trello": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "linear": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "clickup": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "slack": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "graphql playground": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "apollo studio": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "rest client": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "zeplin": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "principle": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "bundler": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "rollup": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "parcel": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "esbuild": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "gulp": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "bazel": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "playwright": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "selenium": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "specflow": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "earl grey": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "gatling": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "locust": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "artillery": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "blazemeter": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "lettuce": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "apache flink": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "apache pulsar": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "azure blob storage": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "delta lake": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "apache drill": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "apache impala": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "luigi": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "metasploit": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "qualys": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "saml": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "ldap": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "keycloak": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "ssl": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "tls": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "elliptic curve": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "blockchain": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "truffle": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "oculus": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "hololens": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "bluetooth": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "microsoft power platform": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "salesforce": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "airtable": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "bubble": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "webflow": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "retool": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "agile": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "lean": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "waterfall": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "test driven development": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "behavior driven development": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "clean code": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "solid principles": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "monolith": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "hexagonal architecture": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "clean architecture": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "graphql": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "technical writing": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "team leadership": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "stakeholder management": {"years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, "go": {"years": null, "confidence": 0.8077688217163086, "context": "Google", "source": "ner_entity"}, "docker swarm": {"years": null, "confidence": 0.7847576141357422, "context": "<PERSON>er", "source": "ner_entity"}, "entity framework": {"years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, "django rest framework": {"years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, "transformers": {"years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, "amazon documentdb": {"years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, "prometheus": {"years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, "jmeter": {"years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, "metamask": {"years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, "a-frame": {"years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, "documentation": {"years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, "mentoring": {"years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, "requirements gathering": {"years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, "core data": {"years": null, "confidence": 0.7046172022819519, "context": "Data", "source": "ner_entity"}, "databricks": {"years": null, "confidence": 0.7046172022819519, "context": "Data", "source": "ner_entity"}, "datadog": {"years": null, "confidence": 0.7046172022819519, "context": "Data", "source": "ner_entity"}}, "categorized_skills": {"programming_languages": [{"skill": "javascript", "years": null, "confidence": 1.0, "context": "istrict 7, Ho <PERSON> City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflow Google Bi", "source": "pattern_match"}, {"skill": "python", "years": null, "confidence": 1.0, "context": "gmail, com District 7, Ho <PERSON> Minh City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS A", "source": "pattern_match"}, {"skill": "java", "years": null, "confidence": 1.0, "context": "com District 7, Ho Chi Minh City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflo", "source": "pattern_match"}, {"skill": "sql", "years": null, "confidence": 1.0, "context": "Ho Chi Minh City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflow Google Big Qu", "source": "pattern_match"}, {"skill": "c", "years": 10, "confidence": 0.9985169172286987, "context": "Ho Chi Minh City", "source": "ner_entity"}, {"skill": "v", "years": 1, "confidence": 0.9933662414550781, "context": "Vietnamese English", "source": "ner_entity"}, {"skill": "r", "years": 10, "confidence": 0.9649661779403687, "context": "Software Engineering University of Science", "source": "ner_entity"}, {"skill": "kotlin", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "scala", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "matlab", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "html", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "less", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "stylus", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "assembly", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "webassembly", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "haskell", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "clojure", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "erlang", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "elixir", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "ocaml", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "lisp", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "powershell", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "perl", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "lua", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "tcl", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "plsql", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "t-sql", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "nosql", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "solidity", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "julia", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "crystal", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "go", "years": null, "confidence": 0.8077688217163086, "context": "Google", "source": "ner_entity"}], "data_science_ai_ml": [{"skill": "docker", "years": null, "confidence": 1.0, "context": "gle Big Query AWS EMR AWS Redshift Hive Terraform Docker HOANG PHAN W ORK EXPERIENCEPROFILE Results-driven", "source": "pattern_match"}, {"skill": "spark", "years": null, "confidence": 1.0, "context": "uages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflow Google Big Query AWS EMR AWS Redshif", "source": "pattern_match"}, {"skill": "bert", "years": null, "confidence": 1.0, "context": "views using state-of-the-art NLP models (ULMFi T, BERT), reducing manual moderation workload by 75%.\nBui", "source": "pattern_match"}, {"skill": "hive", "years": null, "confidence": 1.0, "context": "DFS Airflow Google Big Query AWS EMR AWS Redshift Hive Terraform Docker HOANG PHAN W ORK EXPERIENCEPROFI", "source": "pattern_match"}, {"skill": "pyspark", "years": null, "confidence": 0.9442412853240967, "context": "Spark", "source": "ner_entity"}, {"skill": "google colab", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "tensorflow", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "scikit-learn", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "lightgbm", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "mlflow", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "kubeflow", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "polars", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "plotly", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "altair", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "tableau", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "looker", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "llama", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "stable diffusion", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "pillow", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "albumentations", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "seldon", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "bent<PERSON>l", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "tensorflow serving", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "apache airflow", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "jupyterlab", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "kaggle", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "flink", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "transformers", "years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, {"skill": "databricks", "years": null, "confidence": 0.7046172022819519, "context": "Data", "source": "ner_entity"}], "databases": [{"skill": "solr", "years": null, "confidence": 1.0, "context": "solution using Learning to Rank (LTR) models into SOLR, increasing search conversion rates by 15%.\nEngin", "source": "pattern_match"}, {"skill": "memcached", "years": null, "confidence": 0.9736388921737671, "context": "MCA", "source": "ner_entity"}, {"skill": "google bigtable", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "google bigquery", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "mysql", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "postgresql", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "sqlite", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "oracle", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "sql server", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "leveldb", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "amazon simpledb", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "influxdb", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "timescaledb", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "elasticsearch", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "algolia", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "snowflake", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "clickhouse", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "voltdb", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "amazon documentdb", "years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, {"skill": "prometheus", "years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}], "devops_cicd": [{"skill": "mesos", "years": null, "confidence": 1.0, "context": "onstructed a scalable Data Science Platform using Mesos, Spark, and HDFS, empowering the data science tea", "source": "pattern_match"}, {"skill": "terraform", "years": null, "confidence": 1.0, "context": "irflow Google Big Query AWS EMR AWS Redshift Hive Terraform Docker HOANG PHAN W ORK EXPERIENCEPROFILE Results", "source": "pattern_match"}, {"skill": "buildah", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "helm", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "linkerd", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "ansible", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "saltstack", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "pulumi", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "cloudformation", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "arm templates", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "gitlab ci", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "buildkite", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "flux", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "elk stack", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "logstash", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "new relic", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "splunk", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "opentelemetry", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "fluentd", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "fluent bit", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "vault", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "consul", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "falco", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "twistlock", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "docker swarm", "years": null, "confidence": 0.7847576141357422, "context": "<PERSON>er", "source": "ner_entity"}, {"skill": "datadog", "years": null, "confidence": 0.7046172022819519, "context": "Data", "source": "ner_entity"}], "big_data_analytics": [{"skill": "hdfs", "years": null, "confidence": 1.0, "context": "Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflow Google Big Query AWS EMR AWS Redshift Hiv", "source": "pattern_match"}, {"skill": "spark sql", "years": null, "confidence": 0.9442412853240967, "context": "Spark", "source": "ner_entity"}, {"skill": "apache spark", "years": null, "confidence": 0.9442412853240967, "context": "Spark", "source": "ner_entity"}, {"skill": "google pub/sub", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "google cloud storage", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "apache flink", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "apache pulsar", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "azure blob storage", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "delta lake", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "apache drill", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "apache impala", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "luigi", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}], "backend_frameworks": [{"skill": "fiber", "years": null, "confidence": 1.0, "context": "e NECA project, a network monitoring solution for fiber optic cable infrastructure.\nLANGUAGES Vietnamese", "source": "pattern_match"}, {"skill": "gin", "years": 1, "confidence": 0.9649661779403687, "context": "Software Engineering University of Science", "source": "ner_entity"}, {"skill": "flask", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "bottle", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "falcon", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "starlette", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "play framework", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "blazor", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "laravel", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "phalcon", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "rails", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "ruby on rails", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "revel", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "celery", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "entity framework", "years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, {"skill": "django rest framework", "years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}], "cloud_platforms": [{"skill": "aws", "years": null, "confidence": 1.0, "context": "Data & Cloud Spark HDFS Airflow Google Big Query AWS EMR AWS Redshift Hive Terraform Docker HOANG PHAN", "source": "pattern_match"}, {"skill": "gcp", "years": null, "confidence": 1.0, "context": "plications.\nMigrate our data platform from OCI to GCP with limited downtime.\nEngineered and launched a", "source": "pattern_match"}, {"skill": "google cloud platform", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "google cloud", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "google cloud functions", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "google cloud run", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "google gke", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "alibaba cloud", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "tencent cloud", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "oracle cloud", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "ibm cloud", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "digitalocean", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "linode", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "vultr", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "vercel", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "netlify", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "railway", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "fly.io", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "cloudflare pages", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "aws lambda", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "cloudflare workers", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "deno deploy", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}], "development_tools": [{"skill": "google meet", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "gitlab", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "mercurial", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "visual studio", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "intellij idea", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "eclipse", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "sublime text", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "confluence", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "trello", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "linear", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "clickup", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "slack", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "graphql playground", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "apollo studio", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "rest client", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "z<PERSON>lin", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "principle", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "bundler", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "rollup", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "parcel", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "esbuild", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "gulp", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "bazel", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}], "emerging_technologies": [{"skill": "lora", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "blockchain", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "truffle", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "oculus", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "hololens", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "bluetooth", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "microsoft power platform", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "salesforce", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "airtable", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "bubble", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "webflow", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "retool", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "metamask", "years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, {"skill": "a-frame", "years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}], "frontend_frameworks": [{"skill": "angular", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "<PERSON><PERSON>s", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "svelte", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "sveltekit", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "solid.js", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "lit", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "stencil", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "tailwind css", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "bulma", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "material ui", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "styled-components", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}], "mobile_development": [{"skill": "gradle", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "flutter", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "kotlin multiplatform", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "unreal engine", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "core data", "years": null, "confidence": 0.7046172022819519, "context": "Data", "source": "ner_entity"}], "testing_qa": [{"skill": "playwright", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "selenium", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "specflow", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "earl grey", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "gatling", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "locust", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "artillery", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "blazemeter", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "lettuce", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "jmeter", "years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}], "cybersecurity": [{"skill": "metasploit", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "qualys", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "saml", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "ldap", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "keycloak", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "ssl", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "tls", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "elliptic curve", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}], "soft_skills_methodologies": [{"skill": "agile", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "lean", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "waterfall", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "test driven development", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "behavior driven development", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "clean code", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "solid principles", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "monolith", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "hexagonal architecture", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "clean architecture", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "graphql", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "technical writing", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "team leadership", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "stakeholder management", "years": null, "confidence": 0.8887915015220642, "context": "L", "source": "ner_entity"}, {"skill": "documentation", "years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, {"skill": "mentoring", "years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}, {"skill": "requirements gathering", "years": null, "confidence": 0.7068102359771729, "context": "Me", "source": "ner_entity"}]}, "additional_info": {"total_experience": 10, "education": ["bachelor of software engineering university of science (hcmus) build an ai platform that supports building llm applications", "mail, com district 7, ho chi minh city languages python java javascript sql big data & cloud spark hdfs airflow google big query aws emr aws redshift hive terraform docker ho<PERSON> phan w ork experienceprofile results-driven and innovative lead data engineer with over 10 years of experience designing, building, and optimizing data platforms and machine learning solutions from the ground up", "ms.\nseeking to leverage deep technical knowledge to drive data innovation in a challenging senior engineering role", "<PERSON>ra lead data engineer 2020 - 2021 global fashion group senior data engineer 2018 - 2020 adia - lead data engineer lead data engineer bachelor of software engineering university of science (hcmus) build an ai platform that supports building llm applications", "marts) using open-source technologies, processing over 1tb of data daily to support critical business intelligence", "mated data migration from legacy systems to google big query using a cdc solution, ensuring zero downtime and improving data accessibility for the analytics team", "making cycle", "maintained and scaled data infrastructure, developing new etl pipelines with airflow and emr to ingest data from multiple global regions into a centralized data lake", "bs, reducing execution time by 30% and saving significant cloud computing costs", "mation from unstructured chat data, improving the efficiency of the compliance team by 40%", "machine learning system for the automated approval of customer reviews using state-of-the-art nlp models (ulmfi t, bert), reducing manual moderation workload by 75%", "mail and web platforms, which increased user engagement and drove a 10% uplift in cross-sell revenue", "back, providing actionable insights into company culture and performance across key dimensions", "management, order orchestration, and fulfillment systems", "mation from competitor websites, supporting competitive pricing strategies", "ma solution junior developer contributed to the development of the neca project, a network monitoring solution for fiber optic cable infrastructure"], "certifications": ["aws emr aws redshift hive terraform docker hoang phan w ork experienceprofile results-driven and innovative lead data engineer with over 10 years of experience designing, building, and optimizing data platforms and machine learning solutions from the ground up"], "projects": [], "contact_info": {}}, "summary": {"total_skills_found": 247, "skills_by_category": {"programming_languages": 32, "data_science_ai_ml": 30, "databases": 20, "devops_cicd": 27, "big_data_analytics": 12, "backend_frameworks": 16, "cloud_platforms": 22, "development_tools": 23, "emerging_technologies": 14, "frontend_frameworks": 11, "mobile_development": 5, "testing_qa": 10, "cybersecurity": 8, "soft_skills_methodologies": 17}, "category_scores": {"programming_languages": 0.9737010598182678, "data_science_ai_ml": 0.8932624419530233, "databases": 0.880396169424057, "devops_cicd": 0.8863547907935249, "big_data_analytics": 0.9073005070288976, "backend_frameworks": 0.8837863303720951, "cloud_platforms": 0.8989013650200584, "development_tools": 0.8887915015220642, "emerging_technologies": 0.862794177872794, "frontend_frameworks": 0.8887915015220642, "mobile_development": 0.8519566416740417, "testing_qa": 0.870593374967575, "cybersecurity": 0.8887915015220642, "soft_skills_methodologies": 0.8566771605435539}, "top_skills": [{"skill": "c", "years": 10, "confidence": 0.9985169172286987, "context": "Ho Chi Minh City", "source": "ner_entity"}, {"skill": "r", "years": 10, "confidence": 0.9649661779403687, "context": "Software Engineering University of Science", "source": "ner_entity"}, {"skill": "v", "years": 1, "confidence": 0.9933662414550781, "context": "Vietnamese English", "source": "ner_entity"}, {"skill": "gin", "years": 1, "confidence": 0.9649661779403687, "context": "Software Engineering University of Science", "source": "ner_entity"}, {"skill": "javascript", "years": null, "confidence": 1.0, "context": "istrict 7, Ho <PERSON> City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflow Google Bi", "source": "pattern_match"}, {"skill": "python", "years": null, "confidence": 1.0, "context": "gmail, com District 7, Ho <PERSON> Minh City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS A", "source": "pattern_match"}, {"skill": "java", "years": null, "confidence": 1.0, "context": "com District 7, Ho Chi Minh City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflo", "source": "pattern_match"}, {"skill": "sql", "years": null, "confidence": 1.0, "context": "Ho Chi Minh City Languages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflow Google Big Qu", "source": "pattern_match"}, {"skill": "docker", "years": null, "confidence": 1.0, "context": "gle Big Query AWS EMR AWS Redshift Hive Terraform Docker HOANG PHAN W ORK EXPERIENCEPROFILE Results-driven", "source": "pattern_match"}, {"skill": "spark", "years": null, "confidence": 1.0, "context": "uages Python Java Javascript SQL Big Data & Cloud Spark HDFS Airflow Google Big Query AWS EMR AWS Redshif", "source": "pattern_match"}], "total_experience_years": 10, "education_count": 16, "certifications_count": 1, "has_contact_info": false, "strongest_category": "programming_languages"}, "personal_info": {"name": null, "email": null, "phone": null, "location": "ULMFi T, BE", "linkedin": null, "github": null, "portfolio": null, "summary": null, "objective": null}, "role_analysis": {"best_fit_role": "machine_learning_engineer", "role_rankings": [["machine_learning_engineer", 349.0], ["data_scientist", 346.0], ["devops_engineer", 303.5], ["backend_developer", 286.0], ["data_engineer", 284.0], ["cloud_architect", 253.5], ["full_stack_developer", 239.0], ["blockchain_developer", 225.0], ["frontend_developer", 223.0], ["qa_engineer", 220.0], ["mobile_developer", 214.0], ["security_engineer", 200.0]], "detailed_analysis": {"data_scientist": {"score": 346.0, "required_skills_match": ["python", "r", "sql", "scikit-learn"], "preferred_skills_match": ["tensorflow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plotly", "spark", "aws"], "missing_required_skills": ["pandas", "numpy", "jup<PERSON><PERSON>"], "missing_preferred_skills": ["pytorch", "seaborn"], "keyword_matches": ["machine learning"], "experience_level": "entry", "fit_percentage": 61.428571428571416, "strengths": [], "recommendations": ["Develop skills in: pandas, numpy, jupyter", "Gain more experience (current: 1.1, required: 2)"], "category_scores": {"programming_languages": 80.0, "data_science_ai_ml": 105.0, "databases": 31.5, "cloud_platforms": 22.0, "big_data_analytics": 19.5}}, "data_engineer": {"score": 284.0, "required_skills_match": ["python", "sql", "spark", "aws"], "preferred_skills_match": ["docker", "terraform", "snowflake"], "missing_required_skills": ["airflow", "kafka", "hadoop"], "missing_preferred_skills": ["kubernetes", "redshift", "big<PERSON>y"], "keyword_matches": ["etl", "data lake"], "experience_level": "entry", "fit_percentage": 54.99999999999999, "strengths": [], "recommendations": ["Develop skills in: airflow, kafka, hadoop", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"programming_languages": 64.0, "databases": 52.5, "big_data_analytics": 32.5, "cloud_platforms": 44.0, "devops_cicd": 30.0}}, "full_stack_developer": {"score": 239.0, "required_skills_match": ["javascript", "html"], "preferred_skills_match": ["docker", "aws", "postgresql"], "missing_required_skills": ["typescript", "css", "react", "node.js", "express"], "missing_preferred_skills": ["next.js", "mongodb", "git"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 35.0, "strengths": [], "recommendations": ["Develop skills in: typescript, css, react", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 80.0, "frontend_frameworks": 27.5, "backend_frameworks": 32.0, "databases": 31.5, "cloud_platforms": 33.0}}, "backend_developer": {"score": 286.0, "required_skills_match": ["java", "python", "sql"], "preferred_skills_match": ["docker", "aws", "postgresql"], "missing_required_skills": ["spring", "rest", "api", "microservices"], "missing_preferred_skills": ["kubernetes", "redis", "mongodb"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 45.0, "strengths": [], "recommendations": ["Develop skills in: spring, rest, api", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 96.0, "backend_frameworks": 40.0, "databases": 42.0, "cloud_platforms": 33.0, "devops_cicd": 30.0}}, "frontend_developer": {"score": 223.0, "required_skills_match": ["javascript", "html"], "preferred_skills_match": ["angular", "tailwind css"], "missing_required_skills": ["typescript", "css", "react", "responsive design"], "missing_preferred_skills": ["vue", "sass", "webpack", "figma"], "keyword_matches": ["ui", "component"], "experience_level": "entry", "fit_percentage": 33.33333333333333, "strengths": [], "recommendations": ["Develop skills in: typescript, css, react", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 80.0, "frontend_frameworks": 44.0, "development_tools": 48.00000000000001, "testing_qa": 15.0}}, "devops_engineer": {"score": 303.5, "required_skills_match": ["docker", "aws", "terraform"], "preferred_skills_match": ["ansible", "prometheus", "helm", "vault"], "missing_required_skills": ["kubernetes", "jenkins", "ci/cd"], "missing_preferred_skills": ["grafana", "istio"], "keyword_matches": ["infrastructure", "monitoring"], "experience_level": "entry", "fit_percentage": 55.0, "strengths": [], "recommendations": ["Develop skills in: kube<PERSON><PERSON>, jenkins, ci/cd", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"devops_cicd": 120.0, "cloud_platforms": 66.0, "programming_languages": 48.0, "cybersecurity": 13.499999999999998}}, "mobile_developer": {"score": 214.0, "required_skills_match": ["kotlin", "flutter"], "preferred_skills_match": [], "missing_required_skills": ["swift", "react native", "ios", "android"], "missing_preferred_skills": ["<PERSON><PERSON>", "jetpack compose", "firebase", "xcode", "android studio"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 23.33333333333333, "strengths": [], "recommendations": ["Develop skills in: swift, react native, ios", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 96.0, "mobile_development": 28.000000000000004, "development_tools": 48.00000000000001, "cloud_platforms": 22.0}}, "machine_learning_engineer": {"score": 349.0, "required_skills_match": ["python", "tensorflow", "docker", "mlflow"], "preferred_skills_match": ["kubeflow", "aws", "spark"], "missing_required_skills": ["pytorch", "kubernetes"], "missing_preferred_skills": ["airflow", "mlops", "model deployment"], "keyword_matches": ["ai", "machine learning"], "experience_level": "entry", "fit_percentage": 61.66666666666666, "strengths": [], "recommendations": ["Develop skills in: pytorch, kubernetes", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"programming_languages": 64.0, "data_science_ai_ml": 120.0, "cloud_platforms": 44.0, "devops_cicd": 60.0}}, "security_engineer": {"score": 200.0, "required_skills_match": [], "preferred_skills_match": ["aws", "terraform", "vault"], "missing_required_skills": ["cybersecurity", "owasp", "penetration testing", "vulnerability assessment"], "missing_preferred_skills": ["burp suite", "nmap", "wireshark"], "keyword_matches": ["compliance"], "experience_level": "entry", "fit_percentage": 15.0, "strengths": [], "recommendations": ["Develop skills in: cybersecurity, owasp, penetration testing", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"cybersecurity": 45.0, "cloud_platforms": 44.0, "devops_cicd": 45.0, "programming_languages": 48.0}}, "cloud_architect": {"score": 253.5, "required_skills_match": ["aws", "gcp", "terraform"], "preferred_skills_match": ["docker", "helm", "vault"], "missing_required_skills": ["azure", "kubernetes", "microservices"], "missing_preferred_skills": ["serverless", "lambda", "istio"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 50.0, "strengths": [], "recommendations": ["Develop skills in: azure, kubernetes, microservices", "Gain more experience (current: 0.0, required: 3)"], "category_scores": {"cloud_platforms": 88.0, "devops_cicd": 75.0, "backend_frameworks": 32.0, "cybersecurity": 13.499999999999998}}, "qa_engineer": {"score": 220.0, "required_skills_match": ["selenium"], "preferred_skills_match": ["playwright", "jmeter", "docker"], "missing_required_skills": ["cypress", "jest", "junit", "test automation", "api testing"], "missing_preferred_skills": ["postman", "cucumber", "ci/cd"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 26.666666666666664, "strengths": [], "recommendations": ["Develop skills in: cypress, jest, junit", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"testing_qa": 50.0, "programming_languages": 64.0, "development_tools": 36.0, "devops_cicd": 45.0}}, "blockchain_developer": {"score": 225.0, "required_skills_match": ["solidity", "blockchain"], "preferred_skills_match": ["truffle", "metamask", "javascript"], "missing_required_skills": ["ethereum", "web3", "smart contracts"], "missing_preferred_skills": ["hardhat", "defi", "nft"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 43.0, "strengths": [], "recommendations": ["Develop skills in: ethereum, web3, smart contracts", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"emerging_technologies": 64.0, "programming_languages": 96.0, "frontend_frameworks": 16.5, "cybersecurity": 13.499999999999998}}}, "top_3_roles": [["machine_learning_engineer", 349.0], ["data_scientist", 346.0], ["devops_engineer", 303.5]]}, "skill_combinations": {"technology_stacks": [{"stack": "ML Engineering Stack", "completeness": 80.0, "matched_skills": ["python", "tensorflow", "docker", "aws"], "missing_skills": ["kubernetes"], "experience_level": "novice"}, {"stack": "DevOps Stack", "completeness": 60.0, "matched_skills": ["docker", "terraform", "aws"], "missing_skills": ["kubernetes", "jenkins"], "experience_level": "novice"}, {"stack": "<PERSON><PERSON><PERSON>", "completeness": 50.0, "matched_skills": ["python", "postgresql"], "missing_skills": ["django", "redis"], "experience_level": "novice"}, {"stack": "Spring Stack", "completeness": 50.0, "matched_skills": ["java", "mysql"], "missing_skills": ["spring", "maven"], "experience_level": "novice"}, {"stack": "Data Science Stack", "completeness": 40.0, "matched_skills": ["python", "scikit-learn"], "missing_skills": ["pandas", "numpy", "jup<PERSON><PERSON>"], "experience_level": "novice"}, {"stack": "MEAN", "completeness": 25.0, "matched_skills": ["angular"], "missing_skills": ["mongodb", "express", "node.js"], "experience_level": "novice"}, {"stack": "LAMP", "completeness": 25.0, "matched_skills": ["mysql"], "missing_skills": ["linux", "apache", "php"], "experience_level": "novice"}, {"stack": "React Native Stack", "completeness": 25.0, "matched_skills": ["javascript"], "missing_skills": ["react native", "redux", "firebase"], "experience_level": "novice"}, {"stack": "Flutter Stack", "completeness": 25.0, "matched_skills": ["flutter"], "missing_skills": ["dart", "firebase", "android sdk"], "experience_level": "novice"}], "skill_clusters": {"programming_languages": {"skills": [{"skill": "javascript", "years": null, "confidence": 1.0}, {"skill": "python", "years": null, "confidence": 1.0}, {"skill": "java", "years": null, "confidence": 1.0}, {"skill": "go", "years": null, "confidence": 0.8077688217163086}, {"skill": "kotlin", "years": null, "confidence": 0.8887915015220642}, {"skill": "scala", "years": null, "confidence": 0.8887915015220642}, {"skill": "r", "years": 10, "confidence": 0.9649661779403687}, {"skill": "matlab", "years": null, "confidence": 0.8887915015220642}, {"skill": "html", "years": null, "confidence": 0.8887915015220642}, {"skill": "less", "years": null, "confidence": 0.8887915015220642}, {"skill": "stylus", "years": null, "confidence": 0.8887915015220642}, {"skill": "c", "years": 10, "confidence": 0.9985169172286987}, {"skill": "assembly", "years": null, "confidence": 0.8887915015220642}, {"skill": "webassembly", "years": null, "confidence": 0.8887915015220642}, {"skill": "haskell", "years": null, "confidence": 0.8887915015220642}, {"skill": "clojure", "years": null, "confidence": 0.8887915015220642}, {"skill": "erlang", "years": null, "confidence": 0.8887915015220642}, {"skill": "elixir", "years": null, "confidence": 0.8887915015220642}, {"skill": "ocaml", "years": null, "confidence": 0.8887915015220642}, {"skill": "lisp", "years": null, "confidence": 0.8887915015220642}, {"skill": "powershell", "years": null, "confidence": 0.8887915015220642}, {"skill": "perl", "years": null, "confidence": 0.8887915015220642}, {"skill": "lua", "years": null, "confidence": 0.8887915015220642}, {"skill": "tcl", "years": null, "confidence": 0.8887915015220642}, {"skill": "sql", "years": null, "confidence": 1.0}, {"skill": "plsql", "years": null, "confidence": 0.8887915015220642}, {"skill": "t-sql", "years": null, "confidence": 0.8887915015220642}, {"skill": "nosql", "years": null, "confidence": 0.8887915015220642}, {"skill": "solidity", "years": null, "confidence": 0.8887915015220642}, {"skill": "julia", "years": null, "confidence": 0.8887915015220642}, {"skill": "crystal", "years": null, "confidence": 0.8887915015220642}, {"skill": "v", "years": 1, "confidence": 0.9933662414550781}], "count": 32, "avg_experience": 0.65625, "strength_level": "expert"}, "frontend_frameworks": {"skills": [{"skill": "angular", "years": null, "confidence": 0.8887915015220642}, {"skill": "<PERSON><PERSON>s", "years": null, "confidence": 0.8887915015220642}, {"skill": "svelte", "years": null, "confidence": 0.8887915015220642}, {"skill": "sveltekit", "years": null, "confidence": 0.8887915015220642}, {"skill": "solid.js", "years": null, "confidence": 0.8887915015220642}, {"skill": "lit", "years": null, "confidence": 0.8887915015220642}, {"skill": "stencil", "years": null, "confidence": 0.8887915015220642}, {"skill": "tailwind css", "years": null, "confidence": 0.8887915015220642}, {"skill": "bulma", "years": null, "confidence": 0.8887915015220642}, {"skill": "material ui", "years": null, "confidence": 0.8887915015220642}, {"skill": "styled-components", "years": null, "confidence": 0.8887915015220642}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "backend_frameworks": {"skills": [{"skill": "flask", "years": null, "confidence": 0.8887915015220642}, {"skill": "bottle", "years": null, "confidence": 0.8887915015220642}, {"skill": "falcon", "years": null, "confidence": 0.8887915015220642}, {"skill": "starlette", "years": null, "confidence": 0.8887915015220642}, {"skill": "play framework", "years": null, "confidence": 0.8887915015220642}, {"skill": "blazor", "years": null, "confidence": 0.8887915015220642}, {"skill": "entity framework", "years": null, "confidence": 0.7068102359771729}, {"skill": "laravel", "years": null, "confidence": 0.8887915015220642}, {"skill": "phalcon", "years": null, "confidence": 0.8887915015220642}, {"skill": "rails", "years": null, "confidence": 0.8887915015220642}, {"skill": "ruby on rails", "years": null, "confidence": 0.8887915015220642}, {"skill": "gin", "years": 1, "confidence": 0.9649661779403687}, {"skill": "fiber", "years": null, "confidence": 1.0}, {"skill": "revel", "years": null, "confidence": 0.8887915015220642}, {"skill": "django rest framework", "years": null, "confidence": 0.7068102359771729}, {"skill": "celery", "years": null, "confidence": 0.8887915015220642}], "count": 16, "avg_experience": 0.0625, "strength_level": "strong"}, "mobile_development": {"skills": [{"skill": "core data", "years": null, "confidence": 0.7046172022819519}, {"skill": "kotlin", "years": null, "confidence": 0.8887915015220642}, {"skill": "java", "years": null, "confidence": 1.0}, {"skill": "gradle", "years": null, "confidence": 0.8887915015220642}, {"skill": "flutter", "years": null, "confidence": 0.8887915015220642}, {"skill": "kotlin multiplatform", "years": null, "confidence": 0.8887915015220642}, {"skill": "unreal engine", "years": null, "confidence": 0.8887915015220642}], "count": 7, "avg_experience": 0.0, "strength_level": "basic"}, "data_science_ai_ml": {"skills": [{"skill": "tensorflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "scikit-learn", "years": null, "confidence": 0.8887915015220642}, {"skill": "lightgbm", "years": null, "confidence": 0.8887915015220642}, {"skill": "mlflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "kubeflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "polars", "years": null, "confidence": 0.8887915015220642}, {"skill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "years": null, "confidence": 0.8887915015220642}, {"skill": "plotly", "years": null, "confidence": 0.8887915015220642}, {"skill": "altair", "years": null, "confidence": 0.8887915015220642}, {"skill": "tableau", "years": null, "confidence": 0.8887915015220642}, {"skill": "looker", "years": null, "confidence": 0.8887915015220642}, {"skill": "transformers", "years": null, "confidence": 0.7068102359771729}, {"skill": "bert", "years": null, "confidence": 1.0}, {"skill": "llama", "years": null, "confidence": 0.8887915015220642}, {"skill": "stable diffusion", "years": null, "confidence": 0.8887915015220642}, {"skill": "pillow", "years": null, "confidence": 0.8887915015220642}, {"skill": "albumentations", "years": null, "confidence": 0.8887915015220642}, {"skill": "docker", "years": null, "confidence": 1.0}, {"skill": "seldon", "years": null, "confidence": 0.8887915015220642}, {"skill": "bent<PERSON>l", "years": null, "confidence": 0.8887915015220642}, {"skill": "tensorflow serving", "years": null, "confidence": 0.8887915015220642}, {"skill": "apache airflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "jupyterlab", "years": null, "confidence": 0.8887915015220642}, {"skill": "google colab", "years": null, "confidence": 0.8887915015220642}, {"skill": "kaggle", "years": null, "confidence": 0.8887915015220642}, {"skill": "databricks", "years": null, "confidence": 0.7046172022819519}, {"skill": "spark", "years": null, "confidence": 1.0}, {"skill": "pyspark", "years": null, "confidence": 0.9442412853240967}, {"skill": "hive", "years": null, "confidence": 1.0}, {"skill": "flink", "years": null, "confidence": 0.8887915015220642}], "count": 30, "avg_experience": 0.0, "strength_level": "expert"}, "databases": {"skills": [{"skill": "mysql", "years": null, "confidence": 0.8887915015220642}, {"skill": "postgresql", "years": null, "confidence": 0.8887915015220642}, {"skill": "sqlite", "years": null, "confidence": 0.8887915015220642}, {"skill": "oracle", "years": null, "confidence": 0.8887915015220642}, {"skill": "sql server", "years": null, "confidence": 0.8887915015220642}, {"skill": "amazon documentdb", "years": null, "confidence": 0.7068102359771729}, {"skill": "memcached", "years": null, "confidence": 0.9736388921737671}, {"skill": "leveldb", "years": null, "confidence": 0.8887915015220642}, {"skill": "amazon simpledb", "years": null, "confidence": 0.8887915015220642}, {"skill": "google bigtable", "years": null, "confidence": 0.8887915015220642}, {"skill": "influxdb", "years": null, "confidence": 0.8887915015220642}, {"skill": "timescaledb", "years": null, "confidence": 0.8887915015220642}, {"skill": "prometheus", "years": null, "confidence": 0.7068102359771729}, {"skill": "elasticsearch", "years": null, "confidence": 0.8887915015220642}, {"skill": "solr", "years": null, "confidence": 1.0}, {"skill": "algolia", "years": null, "confidence": 0.8887915015220642}, {"skill": "snowflake", "years": null, "confidence": 0.8887915015220642}, {"skill": "google bigquery", "years": null, "confidence": 0.8887915015220642}, {"skill": "databricks", "years": null, "confidence": 0.7046172022819519}, {"skill": "clickhouse", "years": null, "confidence": 0.8887915015220642}, {"skill": "voltdb", "years": null, "confidence": 0.8887915015220642}], "count": 21, "avg_experience": 0.0, "strength_level": "strong"}, "cloud_platforms": {"skills": [{"skill": "aws", "years": null, "confidence": 1.0}, {"skill": "gcp", "years": null, "confidence": 1.0}, {"skill": "google cloud platform", "years": null, "confidence": 0.8887915015220642}, {"skill": "google cloud", "years": null, "confidence": 0.8887915015220642}, {"skill": "alibaba cloud", "years": null, "confidence": 0.8887915015220642}, {"skill": "tencent cloud", "years": null, "confidence": 0.8887915015220642}, {"skill": "oracle cloud", "years": null, "confidence": 0.8887915015220642}, {"skill": "ibm cloud", "years": null, "confidence": 0.8887915015220642}, {"skill": "digitalocean", "years": null, "confidence": 0.8887915015220642}, {"skill": "linode", "years": null, "confidence": 0.8887915015220642}, {"skill": "vultr", "years": null, "confidence": 0.8887915015220642}, {"skill": "vercel", "years": null, "confidence": 0.8887915015220642}, {"skill": "netlify", "years": null, "confidence": 0.8887915015220642}, {"skill": "railway", "years": null, "confidence": 0.8887915015220642}, {"skill": "fly.io", "years": null, "confidence": 0.8887915015220642}, {"skill": "cloudflare pages", "years": null, "confidence": 0.8887915015220642}, {"skill": "aws lambda", "years": null, "confidence": 0.8887915015220642}, {"skill": "google cloud functions", "years": null, "confidence": 0.8887915015220642}, {"skill": "cloudflare workers", "years": null, "confidence": 0.8887915015220642}, {"skill": "deno deploy", "years": null, "confidence": 0.8887915015220642}, {"skill": "google cloud run", "years": null, "confidence": 0.8887915015220642}, {"skill": "google gke", "years": null, "confidence": 0.8887915015220642}], "count": 22, "avg_experience": 0.0, "strength_level": "strong"}, "devops_cicd": {"skills": [{"skill": "docker", "years": null, "confidence": 1.0}, {"skill": "buildah", "years": null, "confidence": 0.8887915015220642}, {"skill": "docker swarm", "years": null, "confidence": 0.7847576141357422}, {"skill": "mesos", "years": null, "confidence": 1.0}, {"skill": "helm", "years": null, "confidence": 0.8887915015220642}, {"skill": "linkerd", "years": null, "confidence": 0.8887915015220642}, {"skill": "terraform", "years": null, "confidence": 1.0}, {"skill": "ansible", "years": null, "confidence": 0.8887915015220642}, {"skill": "saltstack", "years": null, "confidence": 0.8887915015220642}, {"skill": "pulumi", "years": null, "confidence": 0.8887915015220642}, {"skill": "cloudformation", "years": null, "confidence": 0.8887915015220642}, {"skill": "arm templates", "years": null, "confidence": 0.8887915015220642}, {"skill": "gitlab ci", "years": null, "confidence": 0.8887915015220642}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.8887915015220642}, {"skill": "buildkite", "years": null, "confidence": 0.8887915015220642}, {"skill": "flux", "years": null, "confidence": 0.8887915015220642}, {"skill": "prometheus", "years": null, "confidence": 0.7068102359771729}, {"skill": "elk stack", "years": null, "confidence": 0.8887915015220642}, {"skill": "elasticsearch", "years": null, "confidence": 0.8887915015220642}, {"skill": "logstash", "years": null, "confidence": 0.8887915015220642}, {"skill": "datadog", "years": null, "confidence": 0.7046172022819519}, {"skill": "new relic", "years": null, "confidence": 0.8887915015220642}, {"skill": "splunk", "years": null, "confidence": 0.8887915015220642}, {"skill": "opentelemetry", "years": null, "confidence": 0.8887915015220642}, {"skill": "fluentd", "years": null, "confidence": 0.8887915015220642}, {"skill": "fluent bit", "years": null, "confidence": 0.8887915015220642}, {"skill": "vault", "years": null, "confidence": 0.8887915015220642}, {"skill": "consul", "years": null, "confidence": 0.8887915015220642}, {"skill": "falco", "years": null, "confidence": 0.8887915015220642}, {"skill": "twistlock", "years": null, "confidence": 0.8887915015220642}], "count": 30, "avg_experience": 0.0, "strength_level": "expert"}, "development_tools": {"skills": [{"skill": "gitlab", "years": null, "confidence": 0.8887915015220642}, {"skill": "mercurial", "years": null, "confidence": 0.8887915015220642}, {"skill": "visual studio", "years": null, "confidence": 0.8887915015220642}, {"skill": "intellij idea", "years": null, "confidence": 0.8887915015220642}, {"skill": "eclipse", "years": null, "confidence": 0.8887915015220642}, {"skill": "sublime text", "years": null, "confidence": 0.8887915015220642}, {"skill": "confluence", "years": null, "confidence": 0.8887915015220642}, {"skill": "trello", "years": null, "confidence": 0.8887915015220642}, {"skill": "linear", "years": null, "confidence": 0.8887915015220642}, {"skill": "clickup", "years": null, "confidence": 0.8887915015220642}, {"skill": "slack", "years": null, "confidence": 0.8887915015220642}, {"skill": "google meet", "years": null, "confidence": 0.8887915015220642}, {"skill": "graphql playground", "years": null, "confidence": 0.8887915015220642}, {"skill": "apollo studio", "years": null, "confidence": 0.8887915015220642}, {"skill": "rest client", "years": null, "confidence": 0.8887915015220642}, {"skill": "z<PERSON>lin", "years": null, "confidence": 0.8887915015220642}, {"skill": "principle", "years": null, "confidence": 0.8887915015220642}, {"skill": "gradle", "years": null, "confidence": 0.8887915015220642}, {"skill": "bundler", "years": null, "confidence": 0.8887915015220642}, {"skill": "rollup", "years": null, "confidence": 0.8887915015220642}, {"skill": "parcel", "years": null, "confidence": 0.8887915015220642}, {"skill": "esbuild", "years": null, "confidence": 0.8887915015220642}, {"skill": "gulp", "years": null, "confidence": 0.8887915015220642}, {"skill": "bazel", "years": null, "confidence": 0.8887915015220642}], "count": 24, "avg_experience": 0.0, "strength_level": "strong"}, "testing_qa": {"skills": [{"skill": "playwright", "years": null, "confidence": 0.8887915015220642}, {"skill": "selenium", "years": null, "confidence": 0.8887915015220642}, {"skill": "specflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "earl grey", "years": null, "confidence": 0.8887915015220642}, {"skill": "jmeter", "years": null, "confidence": 0.7068102359771729}, {"skill": "gatling", "years": null, "confidence": 0.8887915015220642}, {"skill": "locust", "years": null, "confidence": 0.8887915015220642}, {"skill": "artillery", "years": null, "confidence": 0.8887915015220642}, {"skill": "blazemeter", "years": null, "confidence": 0.8887915015220642}, {"skill": "specflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "lettuce", "years": null, "confidence": 0.8887915015220642}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "big_data_analytics": {"skills": [{"skill": "apache spark", "years": null, "confidence": 0.9442412853240967}, {"skill": "apache flink", "years": null, "confidence": 0.8887915015220642}, {"skill": "apache pulsar", "years": null, "confidence": 0.8887915015220642}, {"skill": "google pub/sub", "years": null, "confidence": 0.8887915015220642}, {"skill": "hdfs", "years": null, "confidence": 1.0}, {"skill": "azure blob storage", "years": null, "confidence": 0.8887915015220642}, {"skill": "google cloud storage", "years": null, "confidence": 0.8887915015220642}, {"skill": "delta lake", "years": null, "confidence": 0.8887915015220642}, {"skill": "apache drill", "years": null, "confidence": 0.8887915015220642}, {"skill": "apache impala", "years": null, "confidence": 0.8887915015220642}, {"skill": "spark sql", "years": null, "confidence": 0.9442412853240967}, {"skill": "apache airflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "luigi", "years": null, "confidence": 0.8887915015220642}], "count": 13, "avg_experience": 0.0, "strength_level": "moderate"}, "cybersecurity": {"skills": [{"skill": "metasploit", "years": null, "confidence": 0.8887915015220642}, {"skill": "qualys", "years": null, "confidence": 0.8887915015220642}, {"skill": "saml", "years": null, "confidence": 0.8887915015220642}, {"skill": "ldap", "years": null, "confidence": 0.8887915015220642}, {"skill": "keycloak", "years": null, "confidence": 0.8887915015220642}, {"skill": "ssl", "years": null, "confidence": 0.8887915015220642}, {"skill": "tls", "years": null, "confidence": 0.8887915015220642}, {"skill": "vault", "years": null, "confidence": 0.8887915015220642}, {"skill": "elliptic curve", "years": null, "confidence": 0.8887915015220642}], "count": 9, "avg_experience": 0.0, "strength_level": "basic"}, "emerging_technologies": {"skills": [{"skill": "blockchain", "years": null, "confidence": 0.8887915015220642}, {"skill": "solidity", "years": null, "confidence": 0.8887915015220642}, {"skill": "metamask", "years": null, "confidence": 0.7068102359771729}, {"skill": "truffle", "years": null, "confidence": 0.8887915015220642}, {"skill": "unreal engine", "years": null, "confidence": 0.8887915015220642}, {"skill": "oculus", "years": null, "confidence": 0.8887915015220642}, {"skill": "hololens", "years": null, "confidence": 0.8887915015220642}, {"skill": "a-frame", "years": null, "confidence": 0.7068102359771729}, {"skill": "bluetooth", "years": null, "confidence": 0.8887915015220642}, {"skill": "lora", "years": null, "confidence": 0.8887915015220642}, {"skill": "microsoft power platform", "years": null, "confidence": 0.8887915015220642}, {"skill": "salesforce", "years": null, "confidence": 0.8887915015220642}, {"skill": "airtable", "years": null, "confidence": 0.8887915015220642}, {"skill": "bubble", "years": null, "confidence": 0.8887915015220642}, {"skill": "webflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "retool", "years": null, "confidence": 0.8887915015220642}], "count": 16, "avg_experience": 0.0, "strength_level": "strong"}, "soft_skills_methodologies": {"skills": [{"skill": "agile", "years": null, "confidence": 0.8887915015220642}, {"skill": "lean", "years": null, "confidence": 0.8887915015220642}, {"skill": "waterfall", "years": null, "confidence": 0.8887915015220642}, {"skill": "test driven development", "years": null, "confidence": 0.8887915015220642}, {"skill": "behavior driven development", "years": null, "confidence": 0.8887915015220642}, {"skill": "clean code", "years": null, "confidence": 0.8887915015220642}, {"skill": "solid principles", "years": null, "confidence": 0.8887915015220642}, {"skill": "monolith", "years": null, "confidence": 0.8887915015220642}, {"skill": "hexagonal architecture", "years": null, "confidence": 0.8887915015220642}, {"skill": "clean architecture", "years": null, "confidence": 0.8887915015220642}, {"skill": "graphql", "years": null, "confidence": 0.8887915015220642}, {"skill": "technical writing", "years": null, "confidence": 0.8887915015220642}, {"skill": "documentation", "years": null, "confidence": 0.7068102359771729}, {"skill": "mentoring", "years": null, "confidence": 0.7068102359771729}, {"skill": "team leadership", "years": null, "confidence": 0.8887915015220642}, {"skill": "stakeholder management", "years": null, "confidence": 0.8887915015220642}, {"skill": "requirements gathering", "years": null, "confidence": 0.7068102359771729}], "count": 17, "avg_experience": 0.0, "strength_level": "strong"}}, "complementary_skills": {}, "skill_gaps": {}, "stack_completeness": {}}, "comprehensive_report": {"candidate_profile": {"personal_info": {"name": null, "email": null, "phone": null, "location": "ULMFi T, BE", "linkedin": null, "github": null, "portfolio": null, "summary": null, "objective": null}, "professional_summary": "None appears to be best suited for a Machine Learning Engineer role. Strong background in programming languages with 32 identified skills. Key expertise includes c, r, v. ", "contact_completeness": {"required_complete": 0, "required_total": 3, "optional_complete": 1, "optional_total": 4, "missing_required": ["name", "email", "phone"], "missing_optional": ["linkedin", "github", "portfolio"], "required_percentage": 0.0, "optional_percentage": 25.0, "overall_score": 7.5}}, "role_recommendations": {"best_fit": "machine_learning_engineer", "top_matches": [["machine_learning_engineer", 349.0], ["data_scientist", 346.0], ["devops_engineer", 303.5]], "detailed_fit_analysis": {"data_scientist": {"score": 346.0, "required_skills_match": ["python", "r", "sql", "scikit-learn"], "preferred_skills_match": ["tensorflow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plotly", "spark", "aws"], "missing_required_skills": ["pandas", "numpy", "jup<PERSON><PERSON>"], "missing_preferred_skills": ["pytorch", "seaborn"], "keyword_matches": ["machine learning"], "experience_level": "entry", "fit_percentage": 61.428571428571416, "strengths": [], "recommendations": ["Develop skills in: pandas, numpy, jupyter", "Gain more experience (current: 1.1, required: 2)"], "category_scores": {"programming_languages": 80.0, "data_science_ai_ml": 105.0, "databases": 31.5, "cloud_platforms": 22.0, "big_data_analytics": 19.5}}, "data_engineer": {"score": 284.0, "required_skills_match": ["python", "sql", "spark", "aws"], "preferred_skills_match": ["docker", "terraform", "snowflake"], "missing_required_skills": ["airflow", "kafka", "hadoop"], "missing_preferred_skills": ["kubernetes", "redshift", "big<PERSON>y"], "keyword_matches": ["etl", "data lake"], "experience_level": "entry", "fit_percentage": 54.99999999999999, "strengths": [], "recommendations": ["Develop skills in: airflow, kafka, hadoop", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"programming_languages": 64.0, "databases": 52.5, "big_data_analytics": 32.5, "cloud_platforms": 44.0, "devops_cicd": 30.0}}, "full_stack_developer": {"score": 239.0, "required_skills_match": ["javascript", "html"], "preferred_skills_match": ["docker", "aws", "postgresql"], "missing_required_skills": ["typescript", "css", "react", "node.js", "express"], "missing_preferred_skills": ["next.js", "mongodb", "git"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 35.0, "strengths": [], "recommendations": ["Develop skills in: typescript, css, react", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 80.0, "frontend_frameworks": 27.5, "backend_frameworks": 32.0, "databases": 31.5, "cloud_platforms": 33.0}}, "backend_developer": {"score": 286.0, "required_skills_match": ["java", "python", "sql"], "preferred_skills_match": ["docker", "aws", "postgresql"], "missing_required_skills": ["spring", "rest", "api", "microservices"], "missing_preferred_skills": ["kubernetes", "redis", "mongodb"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 45.0, "strengths": [], "recommendations": ["Develop skills in: spring, rest, api", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 96.0, "backend_frameworks": 40.0, "databases": 42.0, "cloud_platforms": 33.0, "devops_cicd": 30.0}}, "frontend_developer": {"score": 223.0, "required_skills_match": ["javascript", "html"], "preferred_skills_match": ["angular", "tailwind css"], "missing_required_skills": ["typescript", "css", "react", "responsive design"], "missing_preferred_skills": ["vue", "sass", "webpack", "figma"], "keyword_matches": ["ui", "component"], "experience_level": "entry", "fit_percentage": 33.33333333333333, "strengths": [], "recommendations": ["Develop skills in: typescript, css, react", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 80.0, "frontend_frameworks": 44.0, "development_tools": 48.00000000000001, "testing_qa": 15.0}}, "devops_engineer": {"score": 303.5, "required_skills_match": ["docker", "aws", "terraform"], "preferred_skills_match": ["ansible", "prometheus", "helm", "vault"], "missing_required_skills": ["kubernetes", "jenkins", "ci/cd"], "missing_preferred_skills": ["grafana", "istio"], "keyword_matches": ["infrastructure", "monitoring"], "experience_level": "entry", "fit_percentage": 55.0, "strengths": [], "recommendations": ["Develop skills in: kube<PERSON><PERSON>, jenkins, ci/cd", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"devops_cicd": 120.0, "cloud_platforms": 66.0, "programming_languages": 48.0, "cybersecurity": 13.499999999999998}}, "mobile_developer": {"score": 214.0, "required_skills_match": ["kotlin", "flutter"], "preferred_skills_match": [], "missing_required_skills": ["swift", "react native", "ios", "android"], "missing_preferred_skills": ["<PERSON><PERSON>", "jetpack compose", "firebase", "xcode", "android studio"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 23.33333333333333, "strengths": [], "recommendations": ["Develop skills in: swift, react native, ios", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"programming_languages": 96.0, "mobile_development": 28.000000000000004, "development_tools": 48.00000000000001, "cloud_platforms": 22.0}}, "machine_learning_engineer": {"score": 349.0, "required_skills_match": ["python", "tensorflow", "docker", "mlflow"], "preferred_skills_match": ["kubeflow", "aws", "spark"], "missing_required_skills": ["pytorch", "kubernetes"], "missing_preferred_skills": ["airflow", "mlops", "model deployment"], "keyword_matches": ["ai", "machine learning"], "experience_level": "entry", "fit_percentage": 61.66666666666666, "strengths": [], "recommendations": ["Develop skills in: pytorch, kubernetes", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"programming_languages": 64.0, "data_science_ai_ml": 120.0, "cloud_platforms": 44.0, "devops_cicd": 60.0}}, "security_engineer": {"score": 200.0, "required_skills_match": [], "preferred_skills_match": ["aws", "terraform", "vault"], "missing_required_skills": ["cybersecurity", "owasp", "penetration testing", "vulnerability assessment"], "missing_preferred_skills": ["burp suite", "nmap", "wireshark"], "keyword_matches": ["compliance"], "experience_level": "entry", "fit_percentage": 15.0, "strengths": [], "recommendations": ["Develop skills in: cybersecurity, owasp, penetration testing", "Gain more experience (current: 0.0, required: 2)"], "category_scores": {"cybersecurity": 45.0, "cloud_platforms": 44.0, "devops_cicd": 45.0, "programming_languages": 48.0}}, "cloud_architect": {"score": 253.5, "required_skills_match": ["aws", "gcp", "terraform"], "preferred_skills_match": ["docker", "helm", "vault"], "missing_required_skills": ["azure", "kubernetes", "microservices"], "missing_preferred_skills": ["serverless", "lambda", "istio"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 50.0, "strengths": [], "recommendations": ["Develop skills in: azure, kubernetes, microservices", "Gain more experience (current: 0.0, required: 3)"], "category_scores": {"cloud_platforms": 88.0, "devops_cicd": 75.0, "backend_frameworks": 32.0, "cybersecurity": 13.499999999999998}}, "qa_engineer": {"score": 220.0, "required_skills_match": ["selenium"], "preferred_skills_match": ["playwright", "jmeter", "docker"], "missing_required_skills": ["cypress", "jest", "junit", "test automation", "api testing"], "missing_preferred_skills": ["postman", "cucumber", "ci/cd"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 26.666666666666664, "strengths": [], "recommendations": ["Develop skills in: cypress, jest, junit", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"testing_qa": 50.0, "programming_languages": 64.0, "development_tools": 36.0, "devops_cicd": 45.0}}, "blockchain_developer": {"score": 225.0, "required_skills_match": ["solidity", "blockchain"], "preferred_skills_match": ["truffle", "metamask", "javascript"], "missing_required_skills": ["ethereum", "web3", "smart contracts"], "missing_preferred_skills": ["hardhat", "defi", "nft"], "keyword_matches": [], "experience_level": "entry", "fit_percentage": 43.0, "strengths": [], "recommendations": ["Develop skills in: ethereum, web3, smart contracts", "Gain more experience (current: 0.0, required: 1)"], "category_scores": {"emerging_technologies": 64.0, "programming_languages": 96.0, "frontend_frameworks": 16.5, "cybersecurity": 13.499999999999998}}}, "career_progression": {"current_level": "senior", "next_roles": ["Technical Lead", "Engineering Manager", "Principal Engineer", "Director of Engineering", "CTO"], "progression_timeline": {"1-2 years": "Lead cross-functional initiatives", "2-4 years": "Drive technical strategy and team growth", "4+ years": "Executive leadership or technical fellowship"}, "skill_requirements": {"technical": ["strategic planning", "technology evaluation", "scalability"], "soft_skills": ["team leadership", "business acumen", "vision setting"]}, "leadership_readiness": true}}, "technical_assessment": {"skill_summary": {"total_skills": 247, "skills_with_experience": 4, "average_experience": 5.5, "experience_distribution": {"expert": 2, "advanced": 0, "intermediate": 0, "beginner": 2, "novice": 243}, "most_experienced_skill": "c"}, "technology_stacks": [{"stack": "ML Engineering Stack", "completeness": 80.0, "matched_skills": ["python", "tensorflow", "docker", "aws"], "missing_skills": ["kubernetes"], "experience_level": "novice"}, {"stack": "DevOps Stack", "completeness": 60.0, "matched_skills": ["docker", "terraform", "aws"], "missing_skills": ["kubernetes", "jenkins"], "experience_level": "novice"}, {"stack": "<PERSON><PERSON><PERSON>", "completeness": 50.0, "matched_skills": ["python", "postgresql"], "missing_skills": ["django", "redis"], "experience_level": "novice"}, {"stack": "Spring Stack", "completeness": 50.0, "matched_skills": ["java", "mysql"], "missing_skills": ["spring", "maven"], "experience_level": "novice"}, {"stack": "Data Science Stack", "completeness": 40.0, "matched_skills": ["python", "scikit-learn"], "missing_skills": ["pandas", "numpy", "jup<PERSON><PERSON>"], "experience_level": "novice"}, {"stack": "MEAN", "completeness": 25.0, "matched_skills": ["angular"], "missing_skills": ["mongodb", "express", "node.js"], "experience_level": "novice"}, {"stack": "LAMP", "completeness": 25.0, "matched_skills": ["mysql"], "missing_skills": ["linux", "apache", "php"], "experience_level": "novice"}, {"stack": "React Native Stack", "completeness": 25.0, "matched_skills": ["javascript"], "missing_skills": ["react native", "redux", "firebase"], "experience_level": "novice"}, {"stack": "Flutter Stack", "completeness": 25.0, "matched_skills": ["flutter"], "missing_skills": ["dart", "firebase", "android sdk"], "experience_level": "novice"}], "skill_clusters": {"programming_languages": {"skills": [{"skill": "javascript", "years": null, "confidence": 1.0}, {"skill": "python", "years": null, "confidence": 1.0}, {"skill": "java", "years": null, "confidence": 1.0}, {"skill": "go", "years": null, "confidence": 0.8077688217163086}, {"skill": "kotlin", "years": null, "confidence": 0.8887915015220642}, {"skill": "scala", "years": null, "confidence": 0.8887915015220642}, {"skill": "r", "years": 10, "confidence": 0.9649661779403687}, {"skill": "matlab", "years": null, "confidence": 0.8887915015220642}, {"skill": "html", "years": null, "confidence": 0.8887915015220642}, {"skill": "less", "years": null, "confidence": 0.8887915015220642}, {"skill": "stylus", "years": null, "confidence": 0.8887915015220642}, {"skill": "c", "years": 10, "confidence": 0.9985169172286987}, {"skill": "assembly", "years": null, "confidence": 0.8887915015220642}, {"skill": "webassembly", "years": null, "confidence": 0.8887915015220642}, {"skill": "haskell", "years": null, "confidence": 0.8887915015220642}, {"skill": "clojure", "years": null, "confidence": 0.8887915015220642}, {"skill": "erlang", "years": null, "confidence": 0.8887915015220642}, {"skill": "elixir", "years": null, "confidence": 0.8887915015220642}, {"skill": "ocaml", "years": null, "confidence": 0.8887915015220642}, {"skill": "lisp", "years": null, "confidence": 0.8887915015220642}, {"skill": "powershell", "years": null, "confidence": 0.8887915015220642}, {"skill": "perl", "years": null, "confidence": 0.8887915015220642}, {"skill": "lua", "years": null, "confidence": 0.8887915015220642}, {"skill": "tcl", "years": null, "confidence": 0.8887915015220642}, {"skill": "sql", "years": null, "confidence": 1.0}, {"skill": "plsql", "years": null, "confidence": 0.8887915015220642}, {"skill": "t-sql", "years": null, "confidence": 0.8887915015220642}, {"skill": "nosql", "years": null, "confidence": 0.8887915015220642}, {"skill": "solidity", "years": null, "confidence": 0.8887915015220642}, {"skill": "julia", "years": null, "confidence": 0.8887915015220642}, {"skill": "crystal", "years": null, "confidence": 0.8887915015220642}, {"skill": "v", "years": 1, "confidence": 0.9933662414550781}], "count": 32, "avg_experience": 0.65625, "strength_level": "expert"}, "frontend_frameworks": {"skills": [{"skill": "angular", "years": null, "confidence": 0.8887915015220642}, {"skill": "<PERSON><PERSON>s", "years": null, "confidence": 0.8887915015220642}, {"skill": "svelte", "years": null, "confidence": 0.8887915015220642}, {"skill": "sveltekit", "years": null, "confidence": 0.8887915015220642}, {"skill": "solid.js", "years": null, "confidence": 0.8887915015220642}, {"skill": "lit", "years": null, "confidence": 0.8887915015220642}, {"skill": "stencil", "years": null, "confidence": 0.8887915015220642}, {"skill": "tailwind css", "years": null, "confidence": 0.8887915015220642}, {"skill": "bulma", "years": null, "confidence": 0.8887915015220642}, {"skill": "material ui", "years": null, "confidence": 0.8887915015220642}, {"skill": "styled-components", "years": null, "confidence": 0.8887915015220642}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "backend_frameworks": {"skills": [{"skill": "flask", "years": null, "confidence": 0.8887915015220642}, {"skill": "bottle", "years": null, "confidence": 0.8887915015220642}, {"skill": "falcon", "years": null, "confidence": 0.8887915015220642}, {"skill": "starlette", "years": null, "confidence": 0.8887915015220642}, {"skill": "play framework", "years": null, "confidence": 0.8887915015220642}, {"skill": "blazor", "years": null, "confidence": 0.8887915015220642}, {"skill": "entity framework", "years": null, "confidence": 0.7068102359771729}, {"skill": "laravel", "years": null, "confidence": 0.8887915015220642}, {"skill": "phalcon", "years": null, "confidence": 0.8887915015220642}, {"skill": "rails", "years": null, "confidence": 0.8887915015220642}, {"skill": "ruby on rails", "years": null, "confidence": 0.8887915015220642}, {"skill": "gin", "years": 1, "confidence": 0.9649661779403687}, {"skill": "fiber", "years": null, "confidence": 1.0}, {"skill": "revel", "years": null, "confidence": 0.8887915015220642}, {"skill": "django rest framework", "years": null, "confidence": 0.7068102359771729}, {"skill": "celery", "years": null, "confidence": 0.8887915015220642}], "count": 16, "avg_experience": 0.0625, "strength_level": "strong"}, "mobile_development": {"skills": [{"skill": "core data", "years": null, "confidence": 0.7046172022819519}, {"skill": "kotlin", "years": null, "confidence": 0.8887915015220642}, {"skill": "java", "years": null, "confidence": 1.0}, {"skill": "gradle", "years": null, "confidence": 0.8887915015220642}, {"skill": "flutter", "years": null, "confidence": 0.8887915015220642}, {"skill": "kotlin multiplatform", "years": null, "confidence": 0.8887915015220642}, {"skill": "unreal engine", "years": null, "confidence": 0.8887915015220642}], "count": 7, "avg_experience": 0.0, "strength_level": "basic"}, "data_science_ai_ml": {"skills": [{"skill": "tensorflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "scikit-learn", "years": null, "confidence": 0.8887915015220642}, {"skill": "lightgbm", "years": null, "confidence": 0.8887915015220642}, {"skill": "mlflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "kubeflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "polars", "years": null, "confidence": 0.8887915015220642}, {"skill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "years": null, "confidence": 0.8887915015220642}, {"skill": "plotly", "years": null, "confidence": 0.8887915015220642}, {"skill": "altair", "years": null, "confidence": 0.8887915015220642}, {"skill": "tableau", "years": null, "confidence": 0.8887915015220642}, {"skill": "looker", "years": null, "confidence": 0.8887915015220642}, {"skill": "transformers", "years": null, "confidence": 0.7068102359771729}, {"skill": "bert", "years": null, "confidence": 1.0}, {"skill": "llama", "years": null, "confidence": 0.8887915015220642}, {"skill": "stable diffusion", "years": null, "confidence": 0.8887915015220642}, {"skill": "pillow", "years": null, "confidence": 0.8887915015220642}, {"skill": "albumentations", "years": null, "confidence": 0.8887915015220642}, {"skill": "docker", "years": null, "confidence": 1.0}, {"skill": "seldon", "years": null, "confidence": 0.8887915015220642}, {"skill": "bent<PERSON>l", "years": null, "confidence": 0.8887915015220642}, {"skill": "tensorflow serving", "years": null, "confidence": 0.8887915015220642}, {"skill": "apache airflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "jupyterlab", "years": null, "confidence": 0.8887915015220642}, {"skill": "google colab", "years": null, "confidence": 0.8887915015220642}, {"skill": "kaggle", "years": null, "confidence": 0.8887915015220642}, {"skill": "databricks", "years": null, "confidence": 0.7046172022819519}, {"skill": "spark", "years": null, "confidence": 1.0}, {"skill": "pyspark", "years": null, "confidence": 0.9442412853240967}, {"skill": "hive", "years": null, "confidence": 1.0}, {"skill": "flink", "years": null, "confidence": 0.8887915015220642}], "count": 30, "avg_experience": 0.0, "strength_level": "expert"}, "databases": {"skills": [{"skill": "mysql", "years": null, "confidence": 0.8887915015220642}, {"skill": "postgresql", "years": null, "confidence": 0.8887915015220642}, {"skill": "sqlite", "years": null, "confidence": 0.8887915015220642}, {"skill": "oracle", "years": null, "confidence": 0.8887915015220642}, {"skill": "sql server", "years": null, "confidence": 0.8887915015220642}, {"skill": "amazon documentdb", "years": null, "confidence": 0.7068102359771729}, {"skill": "memcached", "years": null, "confidence": 0.9736388921737671}, {"skill": "leveldb", "years": null, "confidence": 0.8887915015220642}, {"skill": "amazon simpledb", "years": null, "confidence": 0.8887915015220642}, {"skill": "google bigtable", "years": null, "confidence": 0.8887915015220642}, {"skill": "influxdb", "years": null, "confidence": 0.8887915015220642}, {"skill": "timescaledb", "years": null, "confidence": 0.8887915015220642}, {"skill": "prometheus", "years": null, "confidence": 0.7068102359771729}, {"skill": "elasticsearch", "years": null, "confidence": 0.8887915015220642}, {"skill": "solr", "years": null, "confidence": 1.0}, {"skill": "algolia", "years": null, "confidence": 0.8887915015220642}, {"skill": "snowflake", "years": null, "confidence": 0.8887915015220642}, {"skill": "google bigquery", "years": null, "confidence": 0.8887915015220642}, {"skill": "databricks", "years": null, "confidence": 0.7046172022819519}, {"skill": "clickhouse", "years": null, "confidence": 0.8887915015220642}, {"skill": "voltdb", "years": null, "confidence": 0.8887915015220642}], "count": 21, "avg_experience": 0.0, "strength_level": "strong"}, "cloud_platforms": {"skills": [{"skill": "aws", "years": null, "confidence": 1.0}, {"skill": "gcp", "years": null, "confidence": 1.0}, {"skill": "google cloud platform", "years": null, "confidence": 0.8887915015220642}, {"skill": "google cloud", "years": null, "confidence": 0.8887915015220642}, {"skill": "alibaba cloud", "years": null, "confidence": 0.8887915015220642}, {"skill": "tencent cloud", "years": null, "confidence": 0.8887915015220642}, {"skill": "oracle cloud", "years": null, "confidence": 0.8887915015220642}, {"skill": "ibm cloud", "years": null, "confidence": 0.8887915015220642}, {"skill": "digitalocean", "years": null, "confidence": 0.8887915015220642}, {"skill": "linode", "years": null, "confidence": 0.8887915015220642}, {"skill": "vultr", "years": null, "confidence": 0.8887915015220642}, {"skill": "vercel", "years": null, "confidence": 0.8887915015220642}, {"skill": "netlify", "years": null, "confidence": 0.8887915015220642}, {"skill": "railway", "years": null, "confidence": 0.8887915015220642}, {"skill": "fly.io", "years": null, "confidence": 0.8887915015220642}, {"skill": "cloudflare pages", "years": null, "confidence": 0.8887915015220642}, {"skill": "aws lambda", "years": null, "confidence": 0.8887915015220642}, {"skill": "google cloud functions", "years": null, "confidence": 0.8887915015220642}, {"skill": "cloudflare workers", "years": null, "confidence": 0.8887915015220642}, {"skill": "deno deploy", "years": null, "confidence": 0.8887915015220642}, {"skill": "google cloud run", "years": null, "confidence": 0.8887915015220642}, {"skill": "google gke", "years": null, "confidence": 0.8887915015220642}], "count": 22, "avg_experience": 0.0, "strength_level": "strong"}, "devops_cicd": {"skills": [{"skill": "docker", "years": null, "confidence": 1.0}, {"skill": "buildah", "years": null, "confidence": 0.8887915015220642}, {"skill": "docker swarm", "years": null, "confidence": 0.7847576141357422}, {"skill": "mesos", "years": null, "confidence": 1.0}, {"skill": "helm", "years": null, "confidence": 0.8887915015220642}, {"skill": "linkerd", "years": null, "confidence": 0.8887915015220642}, {"skill": "terraform", "years": null, "confidence": 1.0}, {"skill": "ansible", "years": null, "confidence": 0.8887915015220642}, {"skill": "saltstack", "years": null, "confidence": 0.8887915015220642}, {"skill": "pulumi", "years": null, "confidence": 0.8887915015220642}, {"skill": "cloudformation", "years": null, "confidence": 0.8887915015220642}, {"skill": "arm templates", "years": null, "confidence": 0.8887915015220642}, {"skill": "gitlab ci", "years": null, "confidence": 0.8887915015220642}, {"skill": "<PERSON><PERSON>", "years": null, "confidence": 0.8887915015220642}, {"skill": "buildkite", "years": null, "confidence": 0.8887915015220642}, {"skill": "flux", "years": null, "confidence": 0.8887915015220642}, {"skill": "prometheus", "years": null, "confidence": 0.7068102359771729}, {"skill": "elk stack", "years": null, "confidence": 0.8887915015220642}, {"skill": "elasticsearch", "years": null, "confidence": 0.8887915015220642}, {"skill": "logstash", "years": null, "confidence": 0.8887915015220642}, {"skill": "datadog", "years": null, "confidence": 0.7046172022819519}, {"skill": "new relic", "years": null, "confidence": 0.8887915015220642}, {"skill": "splunk", "years": null, "confidence": 0.8887915015220642}, {"skill": "opentelemetry", "years": null, "confidence": 0.8887915015220642}, {"skill": "fluentd", "years": null, "confidence": 0.8887915015220642}, {"skill": "fluent bit", "years": null, "confidence": 0.8887915015220642}, {"skill": "vault", "years": null, "confidence": 0.8887915015220642}, {"skill": "consul", "years": null, "confidence": 0.8887915015220642}, {"skill": "falco", "years": null, "confidence": 0.8887915015220642}, {"skill": "twistlock", "years": null, "confidence": 0.8887915015220642}], "count": 30, "avg_experience": 0.0, "strength_level": "expert"}, "development_tools": {"skills": [{"skill": "gitlab", "years": null, "confidence": 0.8887915015220642}, {"skill": "mercurial", "years": null, "confidence": 0.8887915015220642}, {"skill": "visual studio", "years": null, "confidence": 0.8887915015220642}, {"skill": "intellij idea", "years": null, "confidence": 0.8887915015220642}, {"skill": "eclipse", "years": null, "confidence": 0.8887915015220642}, {"skill": "sublime text", "years": null, "confidence": 0.8887915015220642}, {"skill": "confluence", "years": null, "confidence": 0.8887915015220642}, {"skill": "trello", "years": null, "confidence": 0.8887915015220642}, {"skill": "linear", "years": null, "confidence": 0.8887915015220642}, {"skill": "clickup", "years": null, "confidence": 0.8887915015220642}, {"skill": "slack", "years": null, "confidence": 0.8887915015220642}, {"skill": "google meet", "years": null, "confidence": 0.8887915015220642}, {"skill": "graphql playground", "years": null, "confidence": 0.8887915015220642}, {"skill": "apollo studio", "years": null, "confidence": 0.8887915015220642}, {"skill": "rest client", "years": null, "confidence": 0.8887915015220642}, {"skill": "z<PERSON>lin", "years": null, "confidence": 0.8887915015220642}, {"skill": "principle", "years": null, "confidence": 0.8887915015220642}, {"skill": "gradle", "years": null, "confidence": 0.8887915015220642}, {"skill": "bundler", "years": null, "confidence": 0.8887915015220642}, {"skill": "rollup", "years": null, "confidence": 0.8887915015220642}, {"skill": "parcel", "years": null, "confidence": 0.8887915015220642}, {"skill": "esbuild", "years": null, "confidence": 0.8887915015220642}, {"skill": "gulp", "years": null, "confidence": 0.8887915015220642}, {"skill": "bazel", "years": null, "confidence": 0.8887915015220642}], "count": 24, "avg_experience": 0.0, "strength_level": "strong"}, "testing_qa": {"skills": [{"skill": "playwright", "years": null, "confidence": 0.8887915015220642}, {"skill": "selenium", "years": null, "confidence": 0.8887915015220642}, {"skill": "specflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "earl grey", "years": null, "confidence": 0.8887915015220642}, {"skill": "jmeter", "years": null, "confidence": 0.7068102359771729}, {"skill": "gatling", "years": null, "confidence": 0.8887915015220642}, {"skill": "locust", "years": null, "confidence": 0.8887915015220642}, {"skill": "artillery", "years": null, "confidence": 0.8887915015220642}, {"skill": "blazemeter", "years": null, "confidence": 0.8887915015220642}, {"skill": "specflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "lettuce", "years": null, "confidence": 0.8887915015220642}], "count": 11, "avg_experience": 0.0, "strength_level": "moderate"}, "big_data_analytics": {"skills": [{"skill": "apache spark", "years": null, "confidence": 0.9442412853240967}, {"skill": "apache flink", "years": null, "confidence": 0.8887915015220642}, {"skill": "apache pulsar", "years": null, "confidence": 0.8887915015220642}, {"skill": "google pub/sub", "years": null, "confidence": 0.8887915015220642}, {"skill": "hdfs", "years": null, "confidence": 1.0}, {"skill": "azure blob storage", "years": null, "confidence": 0.8887915015220642}, {"skill": "google cloud storage", "years": null, "confidence": 0.8887915015220642}, {"skill": "delta lake", "years": null, "confidence": 0.8887915015220642}, {"skill": "apache drill", "years": null, "confidence": 0.8887915015220642}, {"skill": "apache impala", "years": null, "confidence": 0.8887915015220642}, {"skill": "spark sql", "years": null, "confidence": 0.9442412853240967}, {"skill": "apache airflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "luigi", "years": null, "confidence": 0.8887915015220642}], "count": 13, "avg_experience": 0.0, "strength_level": "moderate"}, "cybersecurity": {"skills": [{"skill": "metasploit", "years": null, "confidence": 0.8887915015220642}, {"skill": "qualys", "years": null, "confidence": 0.8887915015220642}, {"skill": "saml", "years": null, "confidence": 0.8887915015220642}, {"skill": "ldap", "years": null, "confidence": 0.8887915015220642}, {"skill": "keycloak", "years": null, "confidence": 0.8887915015220642}, {"skill": "ssl", "years": null, "confidence": 0.8887915015220642}, {"skill": "tls", "years": null, "confidence": 0.8887915015220642}, {"skill": "vault", "years": null, "confidence": 0.8887915015220642}, {"skill": "elliptic curve", "years": null, "confidence": 0.8887915015220642}], "count": 9, "avg_experience": 0.0, "strength_level": "basic"}, "emerging_technologies": {"skills": [{"skill": "blockchain", "years": null, "confidence": 0.8887915015220642}, {"skill": "solidity", "years": null, "confidence": 0.8887915015220642}, {"skill": "metamask", "years": null, "confidence": 0.7068102359771729}, {"skill": "truffle", "years": null, "confidence": 0.8887915015220642}, {"skill": "unreal engine", "years": null, "confidence": 0.8887915015220642}, {"skill": "oculus", "years": null, "confidence": 0.8887915015220642}, {"skill": "hololens", "years": null, "confidence": 0.8887915015220642}, {"skill": "a-frame", "years": null, "confidence": 0.7068102359771729}, {"skill": "bluetooth", "years": null, "confidence": 0.8887915015220642}, {"skill": "lora", "years": null, "confidence": 0.8887915015220642}, {"skill": "microsoft power platform", "years": null, "confidence": 0.8887915015220642}, {"skill": "salesforce", "years": null, "confidence": 0.8887915015220642}, {"skill": "airtable", "years": null, "confidence": 0.8887915015220642}, {"skill": "bubble", "years": null, "confidence": 0.8887915015220642}, {"skill": "webflow", "years": null, "confidence": 0.8887915015220642}, {"skill": "retool", "years": null, "confidence": 0.8887915015220642}], "count": 16, "avg_experience": 0.0, "strength_level": "strong"}, "soft_skills_methodologies": {"skills": [{"skill": "agile", "years": null, "confidence": 0.8887915015220642}, {"skill": "lean", "years": null, "confidence": 0.8887915015220642}, {"skill": "waterfall", "years": null, "confidence": 0.8887915015220642}, {"skill": "test driven development", "years": null, "confidence": 0.8887915015220642}, {"skill": "behavior driven development", "years": null, "confidence": 0.8887915015220642}, {"skill": "clean code", "years": null, "confidence": 0.8887915015220642}, {"skill": "solid principles", "years": null, "confidence": 0.8887915015220642}, {"skill": "monolith", "years": null, "confidence": 0.8887915015220642}, {"skill": "hexagonal architecture", "years": null, "confidence": 0.8887915015220642}, {"skill": "clean architecture", "years": null, "confidence": 0.8887915015220642}, {"skill": "graphql", "years": null, "confidence": 0.8887915015220642}, {"skill": "technical writing", "years": null, "confidence": 0.8887915015220642}, {"skill": "documentation", "years": null, "confidence": 0.7068102359771729}, {"skill": "mentoring", "years": null, "confidence": 0.7068102359771729}, {"skill": "team leadership", "years": null, "confidence": 0.8887915015220642}, {"skill": "stakeholder management", "years": null, "confidence": 0.8887915015220642}, {"skill": "requirements gathering", "years": null, "confidence": 0.7068102359771729}], "count": 17, "avg_experience": 0.0, "strength_level": "strong"}}, "technical_strengths": ["Complete mastery of ML Engineering Stack technology stack", "Strong programming languages expertise with 32 skills", "Strong backend frameworks expertise with 16 skills", "Strong data science ai ml expertise with 30 skills", "Strong databases expertise with 21 skills"], "skill_gaps": {"data_scientist": {"critical_gaps": ["pandas", "numpy", "jup<PERSON><PERSON>"], "enhancement_opportunities": ["pytorch", "seaborn"]}, "data_engineer": {"critical_gaps": ["airflow", "kafka", "hadoop"], "enhancement_opportunities": ["kubernetes", "redshift", "big<PERSON>y"]}, "full_stack_developer": {"critical_gaps": ["typescript", "css", "react", "node.js", "express"], "enhancement_opportunities": ["next.js", "mongodb", "git"]}}}, "experience_analysis": {"total_experience": 10, "experience_distribution": {"mean_years": 0.08906882591093117, "median_years": 0.0, "std_deviation": 0.8999336139032891, "max_years": 10, "min_years": 0, "distribution_type": "balanced"}, "seniority_indicators": {"leadership_keywords": 0, "architecture_keywords": 0, "mentoring_keywords": 0, "project_management_keywords": 0, "senior_level_skills": 1, "overall_level": "mid", "seniority_score": 4.326720647773279}, "learning_trajectory": "continuous_learner"}, "recommendations": {"immediate_actions": ["Priority: Learn pytorch to meet core requirements for machine learning engineer", "Focus on building practical experience through projects or contributions", "Create a GitHub portfolio showcasing your technical skills"], "skill_development": {"short_term": ["pytorch", "kubernetes"], "medium_term": ["airflow", "mlops", "model deployment"], "long_term": ["kubernetes", "microservices", "system design"]}, "career_advice": ["Ready for senior machine learning engineer roles or technical leadership positions", "Your skills align with multiple roles: consider machine learning engineer, data scientist"]}, "overall_assessment": {"marketability_score": {"overall_score": 88.5, "components": {"skill_diversity": 100, "experience_depth": 100, "role_fit": 61.66666666666666, "modern_skills": 100.0}, "grade": "A", "market_readiness": "high"}, "unique_value_proposition": "Full-stack expertise in ML Engineering Stack with 80% stack completeness", "competitive_advantages": ["Proficiency in high-demand technologies: aws, python, tensorflow", "Versatility to work in multiple roles: machine learning engineer and data scientist", "Broad technical knowledge across 247 different technologies"]}}}