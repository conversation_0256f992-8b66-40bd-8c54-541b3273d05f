#!/usr/bin/env python3
"""
Main entry point for the resume screening application.
"""
import argparse
import os
import sys
import logging
from typing import List

from src.resume_screener import ResumeScreener
from config import Config

def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('resume_screening.log')
        ]
    )

def validate_files(file_paths: List[str]) -> List[str]:
    """Validate that all provided files exist and are PDFs."""
    valid_files = []
    config = Config()
    
    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"Warning: File not found: {file_path}")
            continue
        
        _, ext = os.path.splitext(file_path)
        if ext.lower() not in config.SUPPORTED_FORMATS:
            print(f"Warning: Unsupported file format: {file_path}")
            continue
        
        valid_files.append(file_path)
    
    return valid_files

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Resume Screening Application - Extract skills and experience from PDF resumes"
    )
    
    parser.add_argument(
        'files',
        nargs='+',
        help='Path(s) to PDF resume file(s) to analyze'
    )
    
    parser.add_argument(
        '-o', '--output',
        type=str,
        help='Output file path for results (default: results.json)'
    )
    
    parser.add_argument(
        '-f', '--format',
        choices=['json', 'csv'],
        default='json',
        help='Output format (default: json)'
    )
    
    parser.add_argument(
        '-m', '--model',
        type=str,
        help='Hugging Face model name to use for analysis'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--list-skills',
        action='store_true',
        help='List all predefined skills and exit'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    # List skills if requested
    if args.list_skills:
        config = Config()
        print("Predefined Skills by Category:")
        print("=" * 40)
        for category, skills in config.SOFTWARE_SKILLS.items():
            print(f"\n{category.upper()}:")
            for skill in sorted(skills):
                print(f"  - {skill}")
        return
    
    # Validate input files
    valid_files = validate_files(args.files)
    if not valid_files:
        print("Error: No valid PDF files provided.")
        sys.exit(1)
    
    print(f"Processing {len(valid_files)} resume(s)...")
    
    try:
        # Initialize resume screener
        screener = ResumeScreener(model_name=args.model)
        
        # Process resumes
        if len(valid_files) == 1:
            results = screener.process_resume(valid_files[0])
        else:
            results = screener.process_multiple_resumes(valid_files)
        
        # Determine output path
        if args.output:
            output_path = args.output
        else:
            output_path = f"results.{args.format}"
        
        # Save results
        if screener.save_results(results, output_path, args.format):
            print(f"Results saved to: {output_path}")
        else:
            print("Error: Failed to save results")
            sys.exit(1)
        
        # Print enhanced summary
        if len(valid_files) == 1:
            if results['success']:
                print(f"\n{'='*60}")
                print(f"COMPREHENSIVE RESUME ANALYSIS")
                print(f"{'='*60}")

                # Show extraction details
                extraction_method = results.get('extraction_method', 'unknown')
                print(f"\n🔍 PDF EXTRACTION:")
                print(f"  Method Used: {extraction_method}")
                if 'confidence_score' in results:
                    print(f"  Confidence Score: {results.get('confidence_score', 0):.1f}/100")
                print(f"  Text Length: {len(results.get('raw_text', ''))}")

                # Personal Information
                personal_info = results.get('personal_info', {})
                print(f"\n📋 CANDIDATE PROFILE:")
                print(f"  Name: {personal_info.get('name', 'Not found')}")
                print(f"  Email: {personal_info.get('email', 'Not found')}")
                print(f"  Phone: {personal_info.get('phone', 'Not found')}")
                print(f"  Location: {personal_info.get('location', 'Not found')}")
                if personal_info.get('linkedin'):
                    print(f"  LinkedIn: {personal_info.get('linkedin')}")
                if personal_info.get('github'):
                    print(f"  GitHub: {personal_info.get('github')}")

                # Role Analysis
                role_analysis = results.get('role_analysis', {})
                best_fit = role_analysis.get('best_fit_role', 'N/A').replace('_', ' ').title()
                print(f"\n🎯 BEST FIT ROLE: {best_fit}")

                top_3_roles = role_analysis.get('top_3_roles', [])
                if top_3_roles:
                    print(f"\n📊 TOP ROLE MATCHES:")
                    for i, (role, score) in enumerate(top_3_roles[:3], 1):
                        role_name = role.replace('_', ' ').title()
                        detailed = role_analysis.get('detailed_analysis', {}).get(role, {})
                        fit_percentage = detailed.get('fit_percentage', 0)
                        print(f"  {i}. {role_name}: {fit_percentage:.1f}% fit (Score: {score:.1f})")

                # Technical Assessment
                comprehensive_report = results.get('comprehensive_report', {})
                technical_assessment = comprehensive_report.get('technical_assessment', {})

                print(f"\n💻 TECHNICAL ASSESSMENT:")
                skill_summary = technical_assessment.get('skill_summary', {})
                print(f"  Total Skills: {skill_summary.get('total_skills', 0)}")
                print(f"  Average Experience: {skill_summary.get('average_experience', 0):.1f} years")

                # Show top 20 skills
                skills_data = results.get('skills', {})
                if skills_data:
                    print(f"\n🏆 TOP 20 STRONGEST SKILLS:")
                    # Sort skills by a combination of experience and confidence
                    sorted_skills = sorted(
                        skills_data.items(),
                        key=lambda x: (
                            (x[1].get('years', 0) or 0) * 0.6 +
                            (x[1].get('confidence', 0) or 0) * 0.4
                        ),
                        reverse=True
                    )

                    for i, (skill, info) in enumerate(sorted_skills[:20], 1):
                        years = info.get('years', 0) or 0
                        confidence = info.get('confidence', 0) or 0
                        years_text = f"{years} years" if years > 0 else "No exp. data"
                        print(f"  {i:2d}. {skill:<20} | {years_text:<12} | Confidence: {confidence:.2f}")

                    if len(sorted_skills) > 20:
                        print(f"  ... and {len(sorted_skills) - 20} more skills")

                # Technology Stacks
                tech_stacks = technical_assessment.get('technology_stacks', [])
                if tech_stacks:
                    print(f"\n🔧 TECHNOLOGY STACKS:")
                    for stack in tech_stacks[:3]:
                        print(f"  • {stack['stack']}: {stack['completeness']:.0f}% complete ({stack['experience_level']})")

                # Marketability Score
                overall_assessment = comprehensive_report.get('overall_assessment', {})
                marketability = overall_assessment.get('marketability_score', {})
                print(f"\n📈 MARKETABILITY SCORE: {marketability.get('overall_score', 0):.1f}/100 (Grade: {marketability.get('grade', 'N/A')})")
                print(f"  Market Readiness: {marketability.get('market_readiness', 'N/A').title()}")

                # Unique Value Proposition
                uvp = overall_assessment.get('unique_value_proposition', '')
                if uvp:
                    print(f"\n⭐ UNIQUE VALUE PROPOSITION:")
                    print(f"  {uvp}")

                # Recommendations
                recommendations = comprehensive_report.get('recommendations', {})
                immediate_actions = recommendations.get('immediate_actions', [])
                if immediate_actions:
                    print(f"\n🚀 IMMEDIATE RECOMMENDATIONS:")
                    for i, action in enumerate(immediate_actions, 1):
                        print(f"  {i}. {action}")

                # Skill Development Plan
                skill_development = recommendations.get('skill_development', {})
                if skill_development:
                    print(f"\n📚 SKILL DEVELOPMENT PLAN:")
                    if skill_development.get('short_term'):
                        print(f"  Short-term (1-3 months): {', '.join(skill_development['short_term'])}")
                    if skill_development.get('medium_term'):
                        print(f"  Medium-term (3-6 months): {', '.join(skill_development['medium_term'])}")
                    if skill_development.get('long_term'):
                        print(f"  Long-term (6+ months): {', '.join(skill_development['long_term'])}")

                # Career Progression
                role_recommendations = comprehensive_report.get('role_recommendations', {})
                career_progression = role_recommendations.get('career_progression', {})
                if career_progression:
                    print(f"\n🚀 CAREER PROGRESSION:")
                    print(f"  Current Level: {career_progression.get('current_level', 'N/A').title()}")
                    next_roles = career_progression.get('next_roles', [])
                    if next_roles:
                        print(f"  Next Roles: {', '.join(next_roles[:3])}")
                    print(f"  Leadership Ready: {'Yes' if career_progression.get('leadership_readiness') else 'Not yet'}")

                print(f"\n{'='*60}")

            else:
                print(f"Error processing resume: {results.get('error', 'Unknown error')}")
        else:
            print(f"\nProcessed {results['successful']}/{results['total_files']} resumes successfully")
    
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
