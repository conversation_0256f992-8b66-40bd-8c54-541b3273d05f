#!/usr/bin/env python3
"""
Main entry point for the resume screening application.
"""
import argparse
import os
import sys
import logging
from datetime import datetime
from typing import List

from src.resume_screener import ResumeScreener
from config import Config

def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('resume_screening.log')
        ]
    )

def validate_files(file_paths: List[str]) -> List[str]:
    """Validate that all provided files exist and are PDFs."""
    valid_files = []
    config = Config()
    
    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"Warning: File not found: {file_path}")
            continue
        
        _, ext = os.path.splitext(file_path)
        if ext.lower() not in config.SUPPORTED_FORMATS:
            print(f"Warning: Unsupported file format: {file_path}")
            continue
        
        valid_files.append(file_path)
    
    return valid_files

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Resume Screening Application - Extract skills and experience from PDF resumes"
    )
    
    parser.add_argument(
        'files',
        nargs='+',
        help='Path(s) to PDF resume file(s) to analyze'
    )
    
    parser.add_argument(
        '-o', '--output',
        type=str,
        help='Output file path for results (default: results.json)'
    )
    
    parser.add_argument(
        '-f', '--format',
        choices=['json', 'csv'],
        default='json',
        help='Output format (default: json)'
    )
    
    parser.add_argument(
        '-m', '--model',
        type=str,
        help='Hugging Face model name to use for analysis'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--list-skills',
        action='store_true',
        help='List all predefined skills and exit'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    # List skills if requested
    if args.list_skills:
        config = Config()
        print("Predefined Skills by Category:")
        print("=" * 40)
        for category, skills in config.SOFTWARE_SKILLS.items():
            print(f"\n{category.upper()}:")
            for skill in sorted(skills):
                print(f"  - {skill}")
        return
    
    # Validate input files
    valid_files = validate_files(args.files)
    if not valid_files:
        print("Error: No valid PDF files provided.")
        sys.exit(1)
    
    print(f"Processing {len(valid_files)} resume(s)...")
    
    try:
        # Initialize resume screener
        screener = ResumeScreener(model_name=args.model)
        
        # Process resumes
        if len(valid_files) == 1:
            results = screener.process_resume(valid_files[0])
        else:
            results = screener.process_multiple_resumes(valid_files)
        
        # Determine output path
        if args.output:
            output_path = args.output
        else:
            output_path = f"results.{args.format}"
        
        # Save results
        if screener.save_results(results, output_path, args.format):
            print(f"Results saved to: {output_path}")
        else:
            print("Error: Failed to save results")
            sys.exit(1)
        
        # Print enhanced summary
        if len(valid_files) == 1:
            if results['success']:
                print(f"\n{'='*80}")
                print(f"                    RESUME ANALYSIS REPORT")
                print(f"{'='*80}")

                # Header with candidate name
                personal_info = results.get('personal_info', {})
                candidate_name = personal_info.get('name', 'Candidate')
                print(f"\nCandidate: {candidate_name}")
                print(f"Analysis Date: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}")
                print(f"Resume File: {os.path.basename(results['file_path'])}")

                # Executive Summary Box
                role_analysis = results.get('role_analysis', {})
                best_fit = role_analysis.get('best_fit_role', 'N/A').replace('_', ' ').title()
                comprehensive_report = results.get('comprehensive_report', {})
                marketability = comprehensive_report.get('overall_assessment', {}).get('marketability_score', {})

                print(f"\n┌─ EXECUTIVE SUMMARY " + "─" * 50 + "┐")
                print(f"│ Best Role Match: {best_fit:<45} │")
                print(f"│ Overall Score: {marketability.get('overall_score', 0):.0f}/100 ({marketability.get('grade', 'N/A')}){' ' * 32} │")
                print(f"│ Market Readiness: {marketability.get('market_readiness', 'N/A').title():<40} │")
                print(f"└" + "─" * 66 + "┘")

                # Contact Information
                print(f"\n📞 CONTACT INFORMATION")
                print(f"─" * 40)
                if personal_info.get('email'):
                    print(f"Email:     {personal_info.get('email')}")
                if personal_info.get('phone'):
                    print(f"Phone:     {personal_info.get('phone')}")
                if personal_info.get('location'):
                    print(f"Location:  {personal_info.get('location')}")
                if personal_info.get('linkedin'):
                    print(f"LinkedIn:  {personal_info.get('linkedin')}")
                if personal_info.get('github'):
                    print(f"GitHub:    {personal_info.get('github')}")

                # Check for missing contact info
                missing_contact = []
                if not personal_info.get('email'): missing_contact.append('email')
                if not personal_info.get('phone'): missing_contact.append('phone')
                if missing_contact:
                    print(f"⚠️  Missing: {', '.join(missing_contact)}")

                # Role Fit Analysis
                top_3_roles = role_analysis.get('top_3_roles', [])
                if top_3_roles:
                    print(f"\n🎯 ROLE FIT ANALYSIS")
                    print(f"─" * 40)

                    for i, (role, score) in enumerate(top_3_roles[:3], 1):
                        role_name = role.replace('_', ' ').title()
                        detailed = role_analysis.get('detailed_analysis', {}).get(role, {})
                        fit_percentage = detailed.get('fit_percentage', 0)
                        experience_level = detailed.get('experience_level', 'entry').title()

                        # Create fit level description
                        if fit_percentage >= 80:
                            fit_desc = "Excellent match"
                        elif fit_percentage >= 60:
                            fit_desc = "Good match"
                        elif fit_percentage >= 40:
                            fit_desc = "Moderate match"
                        else:
                            fit_desc = "Limited match"

                        print(f"{i}. {role_name}")
                        print(f"   Match: {fit_percentage:.0f}% ({fit_desc})")
                        print(f"   Level: {experience_level}")

                        # Show key strengths for top role
                        if i == 1:
                            strengths = detailed.get('strengths', [])
                            if strengths:
                                print(f"   Strengths: {strengths[0]}")
                        print()

                # Technical Profile
                technical_assessment = comprehensive_report.get('technical_assessment', {})
                skill_summary = technical_assessment.get('skill_summary', {})
                total_skills = skill_summary.get('total_skills', 0)
                avg_experience = skill_summary.get('average_experience', 0)

                print(f"\n💻 TECHNICAL PROFILE")
                print(f"─" * 40)
                print(f"Total Skills Identified: {total_skills}")
                print(f"Average Experience: {avg_experience:.1f} years")

                # Experience level assessment
                if avg_experience >= 5:
                    exp_level = "Senior level professional"
                elif avg_experience >= 3:
                    exp_level = "Mid-level professional"
                elif avg_experience >= 1:
                    exp_level = "Junior level professional"
                else:
                    exp_level = "Entry level professional"
                print(f"Experience Level: {exp_level}")

                # Show strongest technical areas
                categorized_skills = results.get('categorized_skills', {})
                if categorized_skills:
                    print(f"\n🏆 STRONGEST TECHNICAL AREAS")
                    print(f"─" * 40)

                    category_display_names = {
                        'programming_languages': 'Programming Languages',
                        'frontend_frameworks': 'Frontend Development',
                        'backend_frameworks': 'Backend Development',
                        'mobile_development': 'Mobile Development',
                        'data_science_ai_ml': 'Data Science & AI/ML',
                        'databases': 'Database Technologies',
                        'cloud_platforms': 'Cloud Platforms',
                        'devops_cicd': 'DevOps & CI/CD',
                        'development_tools': 'Development Tools',
                        'testing_qa': 'Testing & Quality Assurance',
                        'big_data_analytics': 'Big Data & Analytics',
                        'cybersecurity': 'Cybersecurity',
                        'emerging_technologies': 'Emerging Technologies',
                        'soft_skills_methodologies': 'Methodologies',
                        'other': 'Other Technologies'
                    }

                    # Calculate and display top technical areas
                    category_strengths = []
                    for category, skills in categorized_skills.items():
                        if skills:
                            avg_years = sum(skill.get('years', 0) or 0 for skill in skills) / len(skills)
                            strength_score = len(skills) * (1 + avg_years * 0.2)

                            category_strengths.append({
                                'category': category,
                                'display_name': category_display_names.get(category, category.replace('_', ' ').title()),
                                'count': len(skills),
                                'avg_years': avg_years,
                                'strength_score': strength_score
                            })

                    # Sort and show top areas
                    category_strengths.sort(key=lambda x: x['strength_score'], reverse=True)

                    for i, cat in enumerate(category_strengths[:5], 1):
                        if cat['avg_years'] > 0:
                            exp_text = f"({cat['avg_years']:.1f} years avg)"
                        else:
                            exp_text = "(experience not specified)"
                        print(f"{i}. {cat['display_name']}: {cat['count']} skills {exp_text}")

                # Key Skills Breakdown
                if categorized_skills:
                    print(f"\n🔧 KEY SKILLS BREAKDOWN")
                    print(f"─" * 40)

                    # Get top categories with skills
                    top_categories = sorted(
                        [(cat, skills) for cat, skills in categorized_skills.items() if skills],
                        key=lambda x: len(x[1]),
                        reverse=True
                    )[:4]  # Show top 4 categories

                    category_display_names = {
                        'programming_languages': 'Programming Languages',
                        'frontend_frameworks': 'Frontend Technologies',
                        'backend_frameworks': 'Backend Technologies',
                        'mobile_development': 'Mobile Development',
                        'data_science_ai_ml': 'Data Science & AI/ML',
                        'databases': 'Database Technologies',
                        'cloud_platforms': 'Cloud Platforms',
                        'devops_cicd': 'DevOps & Infrastructure',
                        'development_tools': 'Development Tools',
                        'testing_qa': 'Testing & QA',
                        'big_data_analytics': 'Big Data & Analytics',
                        'cybersecurity': 'Security',
                        'emerging_technologies': 'Emerging Technologies',
                        'soft_skills_methodologies': 'Methodologies',
                        'other': 'Other Technologies'
                    }

                    for category, skills in top_categories:
                        display_name = category_display_names.get(category, category.replace('_', ' ').title())
                        print(f"\n{display_name}:")

                        # Sort skills by experience and show top ones
                        sorted_skills = sorted(
                            skills,
                            key=lambda x: (x.get('years', 0) or 0, x.get('confidence', 0)),
                            reverse=True
                        )

                        # Show skills in a more natural format
                        skill_list = []
                        for skill in sorted_skills[:6]:  # Top 6 per category
                            years = skill.get('years', 0) or 0
                            if years > 0:
                                skill_list.append(f"{skill['skill']} ({years}y)")
                            else:
                                skill_list.append(skill['skill'])

                        # Display skills in a flowing format
                        if skill_list:
                            skills_text = ", ".join(skill_list)
                            if len(sorted_skills) > 6:
                                skills_text += f", and {len(sorted_skills) - 6} more"
                            print(f"  {skills_text}")

                # Top Skills Summary
                skills_data = results.get('skills', {})
                if skills_data:
                    print(f"\n⭐ TOP SKILLS SUMMARY")
                    print(f"─" * 40)

                    # Sort skills by experience and confidence
                    sorted_skills = sorted(
                        skills_data.items(),
                        key=lambda x: (
                            (x[1].get('years', 0) or 0) * 0.7 +
                            (x[1].get('confidence', 0) or 0) * 0.3
                        ),
                        reverse=True
                    )

                    # Group top skills into a natural sentence
                    top_skills_with_exp = []
                    top_skills_without_exp = []

                    for skill, info in sorted_skills[:8]:
                        years = info.get('years', 0) or 0
                        if years > 0:
                            top_skills_with_exp.append(f"{skill} ({years}y)")
                        else:
                            top_skills_without_exp.append(skill)

                    if top_skills_with_exp:
                        print(f"Strongest skills: {', '.join(top_skills_with_exp[:5])}")

                    if top_skills_without_exp:
                        additional_skills = top_skills_without_exp[:3]
                        if additional_skills:
                            print(f"Additional skills: {', '.join(additional_skills)}")

                    if len(sorted_skills) > 8:
                        print(f"Plus {len(sorted_skills) - 8} additional technologies")

                # Technology Stack Analysis
                tech_stacks = technical_assessment.get('technology_stacks', [])
                if tech_stacks and tech_stacks[0]['completeness'] > 50:
                    print(f"\n🔧 TECHNOLOGY STACK EXPERTISE")
                    print(f"─" * 40)
                    top_stack = tech_stacks[0]
                    print(f"Primary Stack: {top_stack['stack']}")
                    print(f"Completeness: {top_stack['completeness']:.0f}% of stack technologies")
                    print(f"Experience Level: {top_stack['experience_level'].title()}")

                # Professional Assessment
                overall_assessment = comprehensive_report.get('overall_assessment', {})
                uvp = overall_assessment.get('unique_value_proposition', '')
                competitive_advantages = overall_assessment.get('competitive_advantages', [])

                print(f"\n📊 PROFESSIONAL ASSESSMENT")
                print(f"─" * 40)
                print(f"Overall Score: {marketability.get('overall_score', 0):.0f}/100 ({marketability.get('grade', 'N/A')})")
                print(f"Market Readiness: {marketability.get('market_readiness', 'N/A').title()}")

                if uvp:
                    print(f"\nKey Strength: {uvp}")

                if competitive_advantages:
                    print(f"Competitive Edge: {competitive_advantages[0]}")

                # Career Guidance
                role_recommendations = comprehensive_report.get('role_recommendations', {})
                career_progression = role_recommendations.get('career_progression', {})
                recommendations = comprehensive_report.get('recommendations', {})

                print(f"\n🎯 CAREER GUIDANCE")
                print(f"─" * 40)

                if career_progression:
                    current_level = career_progression.get('current_level', 'entry').title()
                    print(f"Current Level: {current_level}")

                    next_roles = career_progression.get('next_roles', [])
                    if next_roles:
                        print(f"Recommended Next Step: {next_roles[0]}")

                    leadership_ready = career_progression.get('leadership_readiness', False)
                    if leadership_ready:
                        print(f"Leadership Potential: Ready for leadership roles")
                    else:
                        print(f"Leadership Potential: Focus on technical growth first")

                # Actionable Recommendations
                immediate_actions = recommendations.get('immediate_actions', [])
                skill_development = recommendations.get('skill_development', {})

                if immediate_actions or skill_development:
                    print(f"\n💡 RECOMMENDATIONS")
                    print(f"─" * 40)

                    if immediate_actions:
                        print(f"Immediate Focus:")
                        for action in immediate_actions[:2]:
                            print(f"  • {action}")

                    if skill_development.get('short_term'):
                        print(f"\nSkill Development Priority:")
                        skills_to_learn = skill_development['short_term'][:3]
                        print(f"  • Learn: {', '.join(skills_to_learn)}")

                    if skill_development.get('medium_term'):
                        medium_skills = skill_development['medium_term'][:2]
                        print(f"  • Expand into: {', '.join(medium_skills)}")

                print(f"\n{'─' * 80}")
                print(f"Report generated by Resume Screening AI • {datetime.now().strftime('%B %Y')}")
                print(f"{'─' * 80}")

            else:
                print(f"Error processing resume: {results.get('error', 'Unknown error')}")
        else:
            print(f"\nProcessed {results['successful']}/{results['total_files']} resumes successfully")
    
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
