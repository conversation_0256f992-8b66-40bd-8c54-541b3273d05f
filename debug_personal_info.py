#!/usr/bin/env python3
"""
Debug script to test personal information extraction.
"""
import sys
import re
from src.pdf_reader import PDFReader
from src.comprehensive_analyzer import ComprehensiveAnaly<PERSON>

def test_personal_info_extraction(pdf_path):
    """Test personal information extraction step by step."""
    
    print(f"🔍 DEBUGGING PERSONAL INFO EXTRACTION")
    print(f"File: {pdf_path}")
    print("=" * 60)
    
    # Step 1: Extract text from PDF
    print("\n1. EXTRACTING TEXT FROM PDF...")
    pdf_reader = PDFReader()
    pdf_result = pdf_reader.extract_text(pdf_path)
    
    if not pdf_result['success']:
        print(f"❌ PDF extraction failed: {pdf_result.get('error')}")
        return
    
    text = pdf_result['text']
    print(f"✅ PDF extraction successful")
    print(f"   Method: {pdf_result.get('method_used', 'unknown')}")
    print(f"   Text length: {len(text)} characters")
    
    # Show first 500 characters of extracted text
    print(f"\n📄 EXTRACTED TEXT PREVIEW:")
    preview = text[:500].replace('\n', '\\n')
    print(f"   {preview}...")
    
    # Step 2: Test structured data extraction (if available)
    structured_data = pdf_result.get('structured_data')
    print(f"\n2. STRUCTURED DATA FROM ADVANCED PARSER:")
    if structured_data:
        print(f"   ✅ Structured data available")
        print(f"   Emails: {structured_data.get('emails', [])}")
        print(f"   Phones: {structured_data.get('phones', [])}")
        print(f"   Names: {structured_data.get('names', [])}")
        print(f"   URLs: {structured_data.get('urls', [])}")
    else:
        print(f"   ⚠️  No structured data available")
    
    # Step 3: Test comprehensive analyzer
    print(f"\n3. TESTING COMPREHENSIVE ANALYZER...")
    analyzer = ComprehensiveAnalyzer()
    personal_info = analyzer.extract_personal_information(text, structured_data)
    
    print(f"   Results:")
    for key, value in personal_info.items():
        if value:
            print(f"   ✅ {key}: {value}")
        else:
            print(f"   ❌ {key}: Not found")
    
    # Step 4: Manual pattern testing
    print(f"\n4. MANUAL PATTERN TESTING...")
    
    # Test email patterns manually
    print(f"\n   📧 EMAIL PATTERN TESTING:")
    email_patterns = [
        r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
        r'(?:Email|E-mail|Mail):\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
        r'([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
        r'([A-Za-z0-9._%+-]+)\s*@\s*([A-Za-z0-9.-]+)\s*\.\s*([A-Z|a-z]{2,})',
        r'([a-zA-Z0-9][a-zA-Z0-9._%+-]*@[a-zA-Z0-9][a-zA-Z0-9.-]*\.[a-zA-Z]{2,})'
    ]
    
    for i, pattern in enumerate(email_patterns, 1):
        matches = re.findall(pattern, text, re.IGNORECASE)
        print(f"      Pattern {i}: {matches}")
    
    # Test name patterns manually
    print(f"\n   👤 NAME PATTERN TESTING:")
    name_patterns = [
        r'^([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)',
        r'(?:Name|Full Name):\s*([A-Za-z\s]+)',
        r'^([A-Z][A-Z\s]+)$',
        r'^([A-Z][a-z]+\s+[A-Z][a-z]+)$',
        r'([A-Z][a-z]+\s+[A-Z]\.\s+[A-Z][a-z]+)',
        r'([A-Z][a-z]+\s+[A-Z][a-z]+\s+[A-Z][a-z]+)'
    ]
    
    lines = text.split('\n')[:10]
    print(f"      First 10 lines:")
    for i, line in enumerate(lines, 1):
        line = line.strip()
        print(f"        {i}: '{line}'")
    
    print(f"\n      Testing name patterns on first 10 lines:")
    for i, pattern in enumerate(name_patterns, 1):
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if len(line) > 3 and len(line) < 60:
                match = re.search(pattern, line)
                if match:
                    candidate_name = match.group(1).strip()
                    if len(candidate_name.split()) >= 2:
                        print(f"        Pattern {i} on line {line_num}: '{candidate_name}'")
    
    # Test phone patterns manually
    print(f"\n   📞 PHONE PATTERN TESTING:")
    phone_patterns = [
        r'(?:Phone|Tel|Mobile|Cell):\s*([\d\s\-\+\(\)\.]+)',
        r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',
        r'(\+?\d{1,3}[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',
        r'(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})',
        r'(\d{3})\s*[-.]?\s*(\d{3})\s*[-.]?\s*(\d{4})'
    ]
    
    for i, pattern in enumerate(phone_patterns, 1):
        matches = re.findall(pattern, text, re.IGNORECASE)
        print(f"      Pattern {i}: {matches}")
    
    # Step 5: Show raw text for manual inspection
    print(f"\n5. RAW TEXT FOR MANUAL INSPECTION:")
    print(f"   First 1000 characters:")
    print(f"   {repr(text[:1000])}")
    
    return personal_info

def test_with_sample_text():
    """Test with known sample text."""
    print(f"\n" + "="*60)
    print(f"TESTING WITH SAMPLE TEXT")
    print(f"="*60)
    
    sample_text = """
    John Doe
    Senior Software Engineer
    
    Email: <EMAIL>
    Phone: (*************
    Location: San Francisco, CA
    LinkedIn: linkedin.com/in/johndoe
    GitHub: github.com/johndoe
    
    PROFESSIONAL SUMMARY
    Experienced software engineer with 8+ years in full-stack development.
    """
    
    print(f"Sample text:")
    print(f"{sample_text}")
    
    analyzer = ComprehensiveAnalyzer()
    personal_info = analyzer.extract_personal_information(sample_text)
    
    print(f"\nExtraction results:")
    for key, value in personal_info.items():
        if value:
            print(f"✅ {key}: {value}")
        else:
            print(f"❌ {key}: Not found")

def main():
    if len(sys.argv) != 2:
        print("Usage: python debug_personal_info.py <pdf_file>")
        print("Example: python debug_personal_info.py 'Hoang Phan CV.pdf'")
        
        # Test with sample text anyway
        test_with_sample_text()
        return
    
    pdf_path = sys.argv[1]
    personal_info = test_personal_info_extraction(pdf_path)
    
    # Also test with sample text for comparison
    test_with_sample_text()
    
    print(f"\n" + "="*60)
    print(f"DEBUGGING COMPLETE")
    print(f"="*60)
    
    if personal_info:
        found_items = [k for k, v in personal_info.items() if v]
        missing_items = [k for k, v in personal_info.items() if not v]
        
        print(f"✅ Found: {', '.join(found_items) if found_items else 'None'}")
        print(f"❌ Missing: {', '.join(missing_items) if missing_items else 'None'}")
        
        if not personal_info.get('email') and not personal_info.get('name'):
            print(f"\n💡 SUGGESTIONS:")
            print(f"   1. Check if the PDF text extraction is working properly")
            print(f"   2. Verify the text contains readable contact information")
            print(f"   3. The PDF might be scanned (try OCR with: pip install pytesseract)")
            print(f"   4. Check if the text format is unusual")

if __name__ == "__main__":
    main()
