# Installation Guide

This guide helps you install the resume screening application with the correct dependencies.

## Quick Start (Recommended)

### Option 1: Minimal Installation
Install core functionality first:

```bash
pip install -r requirements-minimal.txt
```

### Option 2: Flexible Installation
Install with compatible version ranges:

```bash
pip install -r requirements-flexible.txt
```

### Option 3: Step-by-Step Installation
If you encounter conflicts, install dependencies in stages:

```bash
# Stage 1: Core PDF processing
pip install PyPDF2>=3.0.0 pdfplumber>=0.9.0

# Stage 2: AI/ML libraries
pip install transformers>=4.30.0 torch>=2.0.0

# Stage 3: Data processing
pip install pandas>=2.0.0 numpy>=1.24.0

# Stage 4: Configuration and utilities
pip install python-dotenv>=1.0.0 spacy>=3.7.0 regex>=2023.0.0

# Stage 5: Optional advanced features
pip install pymupdf>=1.23.0 sentence-transformers>=2.2.0
```

## Advanced Features (Optional)

### OCR Support
For scanned PDFs, install OCR dependencies:

```bash
pip install pytesseract>=0.3.0 Pillow>=10.0.0 pdf2image>=1.16.3
```

**Additional Setup:**
- **macOS**: `brew install tesseract`
- **Ubuntu**: `sudo apt-get install tesseract-ocr`
- **Windows**: Download from [Tesseract GitHub](https://github.com/UB-Mannheim/tesseract/wiki)

### Alternative PDF Parsers
For better PDF extraction:

```bash
# PyMuPDF (recommended)
pip install pymupdf>=1.23.0

# pdfminer.six (compatible with pdfplumber)
pip install pdfminer.six==20221105

# Camelot for table extraction (optional)
pip install camelot-py[cv]>=0.11.0
```

### AI Model Enhancement
For better text understanding:

```bash
pip install sentence-transformers>=2.2.0 huggingface-hub>=0.16.0
```

## Troubleshooting

### Common Issues

#### 1. pdfminer.six Version Conflict
**Error**: `pdfplumber 0.10.0 depends on pdfminer.six==20221105`

**Solution**: Use compatible versions:
```bash
pip install pdfplumber==0.10.0 pdfminer.six==20221105
```

#### 2. PyTorch Installation Issues
**Error**: Large download or CUDA compatibility issues

**Solution**: Install CPU-only version:
```bash
pip install torch>=2.0.0 --index-url https://download.pytorch.org/whl/cpu
```

#### 3. Camelot Dependencies
**Error**: OpenCV or other CV dependencies missing

**Solution**: Install system dependencies first:
```bash
# macOS
brew install opencv

# Ubuntu
sudo apt-get install python3-opencv

# Then install camelot
pip install camelot-py[cv]
```

#### 4. Tesseract Not Found
**Error**: `TesseractNotFoundError`

**Solution**: Install Tesseract and set path:
```python
# In your code or environment
import pytesseract
pytesseract.pytesseract.tesseract_cmd = '/usr/local/bin/tesseract'  # macOS
# or
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'  # Windows
```

### Virtual Environment (Recommended)

Create a clean environment to avoid conflicts:

```bash
# Create virtual environment
python -m venv resume-screening-env

# Activate it
# macOS/Linux:
source resume-screening-env/bin/activate
# Windows:
resume-screening-env\Scripts\activate

# Install dependencies
pip install -r requirements-minimal.txt

# Test the installation
python test_advanced_parser.py "sample_resume.pdf"
```

## Verification

Test your installation:

```bash
# Test basic functionality
python -c "from src.pdf_reader import PDFReader; print('✅ Core PDF reading works')"

# Test AI functionality
python -c "from src.ai_analyzer import AIAnalyzer; print('✅ AI analysis works')"

# Test advanced parser
python -c "from src.advanced_pdf_parser import AdvancedPDFParser; print('✅ Advanced parser works')"

# Run comprehensive test
python test_comprehensive_skills.py
```

## Performance Tips

1. **Use CPU-only PyTorch** if you don't have GPU:
   ```bash
   pip install torch>=2.0.0 --index-url https://download.pytorch.org/whl/cpu
   ```

2. **Install only needed features**:
   - Skip OCR if you only process text-based PDFs
   - Skip camelot if you don't need table extraction
   - Skip advanced AI models for basic skill extraction

3. **Use lightweight alternatives**:
   ```bash
   # Instead of full transformers
   pip install transformers[torch] --no-deps
   pip install torch tokenizers
   ```

## Docker Installation (Alternative)

If you prefer Docker:

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements-minimal.txt .
RUN pip install -r requirements-minimal.txt

COPY . .
CMD ["python", "main.py"]
```

```bash
docker build -t resume-screening .
docker run -v $(pwd):/app resume-screening python main.py "resume.pdf"
```

## Support

If you continue to have issues:

1. Check Python version: `python --version` (requires 3.8+)
2. Update pip: `pip install --upgrade pip`
3. Clear pip cache: `pip cache purge`
4. Try installing in a fresh virtual environment
5. Check the specific error message and search for solutions

For the most stable experience, use `requirements-minimal.txt` first, then add optional features as needed.
